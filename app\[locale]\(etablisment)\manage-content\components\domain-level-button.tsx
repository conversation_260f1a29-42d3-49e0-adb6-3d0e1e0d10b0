'use client'
import { AccordionContent } from '@/components/ui/accordion'
import React from 'react'
import { Domain } from '@/prisma/generated/zod/modelSchema/DomainSchema'
import { Level } from '@/prisma/generated/zod/modelSchema/LevelSchema'
import { useContentStore } from '../store/content.store'
import { useLocale } from 'next-intl'
import { getLangProps } from '@/lib/utils/string.utils'

type DomainLevelButtonProps = {
    domain: Domain
    level: Level
}

const DomainLevelButton = ({ domain, level }: DomainLevelButtonProps) => {
    const locale = useLocale()
    const { setDomainId, domainId, levelId, setDomain } = useContentStore()
    return (
        <AccordionContent
            className={`ml-12 cursor-pointer ${domainId === domain.id && level.id === levelId ? 'font-extrabold' : ''}`}
            onClick={() => {
                setDomainId(domain.id)
                setDomain(domain)
            }}
        >
            {getLangProps({
                obj: domain,
                base: 'name',
                lang: locale
            })}
        </AccordionContent>
    )
}

export default DomainLevelButton
