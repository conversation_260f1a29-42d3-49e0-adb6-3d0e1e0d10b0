import React, { useEffect, useState } from 'react'
import { Chapter, Part } from '@/lib/training-mode/types'
import CtrlFromDb from './ctrl-from-db'
import useAccountStore from '@/lib/stores/account-store/store'
import { useTranslations } from 'next-intl'
import { useCookies } from 'next-client-cookies'

interface CtrlGeneratorProps {
    getChapters: (domain: string, level: string) => Promise<Chapter[]>
    getParts: (chapterId: string) => Promise<Part[]>
}

function CtrlGenerator({ getChapters, getParts }: CtrlGeneratorProps) {
    const [chapters, setChapters] = useState<Chapter[]>([])
    const { user } = useAccountStore()
    const t = useTranslations('app.mode.create-controle')

    const cookies = useCookies()
    const topic = cookies.get('topic')
    useEffect(() => {
        ;(async () => {
            const chaps = await getChapters(topic!, user.level!)
            setChapters(chaps)
        })()
    }, [getChapters, topic, user])

    // const reset = () => {
    //   resetState()
    // }

    return (
        <div className="w-full h-auto relative flex flex-col gap-12  ">
            {/* <button
        onClick={reset}
        className="flex justify-center items-center top-2 -left- absolute size-8 bg-dinoBotBlue hover:bg-dinoBotBlue/80 text-white rounded-full"
      >
        <ChevronLeft className="size-6 mr-0.5" />
      </button> */}
            <h2 className="text-2xl md:text-3xl text-dinoBotVibrantBlue leading-9 font-extrabold text-center underline mb-5">
                {t('title')}
            </h2>
            <div className="py-8 pt-11 w-[350px] sm:w-[500px] md:w-[600px]  bg-clip-padding  backdrop-blur-md backdrop-opacity-90 bg-white/60 saturate-100 backdrop-contrast-100 border border-dinoBotSky rounded">
                <CtrlFromDb chapters={chapters} getParts={getParts} />
            </div>
        </div>
    )
}

export default CtrlGenerator
