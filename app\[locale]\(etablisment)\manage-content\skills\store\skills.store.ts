import { SkillWithRelations } from '@/prisma/generated/zod/modelSchema/SkillSchema'
import { toast } from 'sonner'
import { create } from 'zustand'
import { createSelectors } from '@/lib/stores/selectors.store'

export type ChapterWithSkillsData = {
    id: string
    title: string
    parts: PartWithSkillsData[]
}

export type PartWithSkillsData = {
    id: string
    name: string
    skills: SkillData[]
}

export type SkillData = {
    id: string
    description: string | null
    createdAt: Date
    updatedAt: Date | null
    partId: string
}

type SkillsStore = SkillsState & SkillsActions
type SkillsState = {
    skills: SkillWithRelations[]
    selectedChapter: ChapterWithSkillsData | undefined
    chaptersWithData: ChapterWithSkillsData[]
    selectedPart: PartWithSkillsData | undefined
    searchFilter: string
}
type SkillsActions = {
    setSkills: (skills: SkillWithRelations[]) => void
    setChaptersWithData: (chaptersWithData: ChapterWithSkillsData[]) => void
    setSelectedChapter: (selectedChapter: ChapterWithSkillsData) => void
    setSelectedPart: (selectedPart: PartWithSkillsData) => void
    setSearchFilter: (searchFilter: string) => void
}

const initialSkills: SkillsState = {
    skills: [],
    selectedChapter: undefined,
    chaptersWithData: [],
    selectedPart: undefined,
    searchFilter: ''
}

export const useSkillsStore = create<SkillsStore>((set, get) => ({
    ...initialSkills,
    setSkills: skills => set({ skills }),

    setChaptersWithData: chaptersWithData => set({ chaptersWithData }),
    setSelectedChapter: selectedChapter => set({ selectedChapter }),
    setSelectedPart: selectedPart => set({ selectedPart }),
    setSearchFilter: searchFilter => set({ searchFilter })
}))

export const getPartsFromSelectedChapter = (
    state: SkillsStore
): PartWithSkillsData[] => {
    const selectedChapter = state.selectedChapter
    if (!selectedChapter) return []

    const chapterWithData = state.chaptersWithData.find(
        ch => ch.id === selectedChapter.id
    )
    return chapterWithData ? chapterWithData.parts : []
}

export const getSkillsFromSelectedPart = (state: SkillsStore): SkillData[] => {
    const selectedPart = state.selectedPart
    if (!selectedPart) return []

    const parts = getPartsFromSelectedChapter(state)
    const partWithData = parts.find(p => p.id === selectedPart.id)
    return partWithData ? partWithData.skills : []
}

export const selectSkillsStore = createSelectors(useSkillsStore)
