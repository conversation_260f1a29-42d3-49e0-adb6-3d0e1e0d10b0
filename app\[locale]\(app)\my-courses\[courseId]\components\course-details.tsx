'use client'
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { useSearchParams, useRouter, usePathname } from 'next/navigation'
import React from 'react'
import AboutCourse from './about/about-course'
import { useLocale, useTranslations } from 'next-intl'
// import EditeurCalendar from './calendar/calendar'
import Scoring from './notation/scoring'
import { getLangDir } from 'rtl-detect'

type CourseDetailsProps = {
    courseId: string
}

function CourseDetails({ courseId }: CourseDetailsProps) {
    const searchParams = useSearchParams()
    const router = useRouter()
    const pathname = usePathname()
    const locale = useLocale()
    const dir = getLangDir(locale)
    const translate = useTranslations('app.courses.index')

    const activeTab = searchParams.get('tab') || 'about'

    // Fonction pour changer l'onglet et mettre à jour l'URL
    const handleTabChange = (tab: string) => {
        const newUrl = `${pathname}?tab=${tab}`
        router.replace(newUrl, { scroll: false }) // Évite de remonter la page
    }

    return (
        <Tabs
            defaultValue={activeTab}
            onValueChange={handleTabChange}
            className="bg-[#fafafa] h-4/5 w-3/4 px-6 py-2 mx-4 my-1 rounded-sm overflow-hidden"
            dir={dir}
        >
            <TabsList className="bg- hover">
                <TabsTrigger
                    value="about"
                    className="rounded-none h-full data-[state=active]:bg- data-[state=active]:shadow-none data-[state=active]:border-b-black data-[state=active]:border-b-2"
                >
                    {translate('tabs.about')}
                </TabsTrigger>
                {/* <TabsTrigger
                    value="calendar"
                    className="rounded-none h-full data-[state=active]:bg- data-[state=active]:shadow-none data-[state=active]:border-b-black data-[state=active]:border-b-2"
                >
                    {translate('tabs.calendar')}
                </TabsTrigger> */}
                <TabsTrigger
                    value="scoring"
                    className="rounded-none h-full data-[state=active]:bg- data-[state=active]:shadow-none data-[state=active]:border-b-black data-[state=active]:border-b-2"
                >
                    {translate('tabs.scoring')}
                </TabsTrigger>
            </TabsList>
            <TabsContent value="about">
                <AboutCourse courseId={courseId} />
            </TabsContent>
            {/* <TabsContent value="calendar" className="h-full">
                <EditeurCalendar />
            </TabsContent> */}
            <TabsContent value="scoring" className="h-full">
                <Scoring />
            </TabsContent>
        </Tabs>
    )
}

export default CourseDetails
