'use client'
// import "//unpkg.com/mathlive";
import React, { useEffect, useRef, useState } from 'react'
const { MathfieldElement } = await import('mathlive')

import { X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import useTrainCortexStore from '@/lib/stores/cortex-store/training-cortex-store'

MathfieldElement.fontsDirectory = '/mathlive-fonts'
MathfieldElement.soundsDirectory = null

function TrainingCortexDialog() {
    const [visible, setVisible] = useState(false)
    const mf = useRef<InstanceType<typeof MathfieldElement>>(null)
    const mfp = useRef<HTMLDivElement>(null)
    const { closeCortexDialog, open, insertNow, setCortexValue, cortexValue } =
        useTrainCortexStore()
    useEffect(() => {
        const El = new MathfieldElement()
        mf.current?.appendChild(El as Node)
        mf.current?.focus()
        window.mathVirtualKeyboard.container = mfp.current
        if (open) {
            setVisible(true)
            window.mathVirtualKeyboard.show()
        } else {
            setVisible(false)
            window.mathVirtualKeyboard.hide()
        }
        mfp.current
            ?.querySelectorAll('[data-tooltip="Lettres romaines"]')
            .forEach(e => e.remove())
    }, [open])
    return (
        <div
            className={`${open ? 'flex' : 'hidden'} bg-slate-100 border-2 border-dinoBotDarkGray rounded-sm flex-col justify-between gap-2 absolute top-1/2 -translate-x-1/2 left-1/2 -translate-y-1/2 size-fit p-2 ${visible ? 'h-80' : 'h-fit'}`}
        >
            <div className="flex h-fit">
                <div className="w-96">
                    {/*  @ts-expect-error - Explanation: math-field can't be imported */}
                    <math-field
                        ref={mf}
                        onInput={(evt: React.ChangeEvent<any>) =>
                            setCortexValue(evt.target.value)
                        }
                        style={{ width: '100%' }}
                    >
                        {cortexValue}
                        {/*  @ts-expect-error - Explanation: math-field can't be imported */}
                    </math-field>
                </div>
                <div className="w-full sm:w-fit h-full flex flex-row-reverse sm:flex-row justify-center items-center gap-2">
                    <Button
                        className="bg-dinoBotRed text-white hover:bg-dinoBotRed/80 transition-all duration-200 rounded h-10"
                        onClick={insertNow}
                    >
                        Insérer
                    </Button>
                    <Button
                        className="h-10"
                        variant={'outline'}
                        onClick={closeCortexDialog}
                    >
                        <X />
                    </Button>
                </div>
            </div>
            <div
                ref={mfp}
                className={`${visible ? 'h-72' : 'h0'} w-[530px]`}
            ></div>
        </div>
    )
}

export default TrainingCortexDialog
