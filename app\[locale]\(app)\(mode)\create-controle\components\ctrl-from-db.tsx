import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import React, { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'

import InfoTooltip from '@/components/ui/info-tooltip'
import { Chapter, Part } from '@/lib/training-mode/types'
import { toast } from 'sonner'
import ErrorTooltip from '@/components/ui/error-tooltip'
import { useRouter } from '@/i18n/routing'
import { selectUseCtrlModeStore } from '@/app/[locale]/(app)/(mode)/controle/store/controle-store'
import { useLocale, useTranslations } from 'next-intl'
import { getLangProps } from '@/lib/utils/string.utils'
import { TimePicker } from '@/components/shared/ui/time-picker'

interface CtrlFromDbProps {
    chapters: Chapter[]
    getParts: (chapterId: string) => Promise<Part[]>
}

function CtrlFromDb({ chapters, getParts }: CtrlFromDbProps) {
    const [selectedChapter, setSelectedChapter] = useState<string | null>(null)
    const [, setParts] = useState<Part[]>([])
    const formData = selectUseCtrlModeStore.use.ctrlInfo()
    const handleFormChange = selectUseCtrlModeStore.use.updateCtrlInfo()
    const time = selectUseCtrlModeStore.use.time()
    const setTime = selectUseCtrlModeStore.use.setTime()
    const [showError, setShowError] = useState<boolean>(false)
    const t = useTranslations('app.mode.create-controle')
    const router = useRouter()
    const locale = useLocale()

    useEffect(() => {
        if (selectedChapter) {
            handleFormChange('chapterId', selectedChapter)
            ;(async () => {
                const parts = await getParts(selectedChapter)
                setParts(parts)
            })()
        }
    }, [getParts, handleFormChange, selectedChapter])

    useEffect(() => {
        //reset selected chapter every time we change domain
        setSelectedChapter(null)
    }, [chapters])

    const submit = () => {
        if (
            formData.chapterId &&
            time &&
            (time.getHours() > 0 ||
                time.getMinutes() > 0 ||
                time.getSeconds() > 0)
        ) {
            setShowError(false)
            router.push('/controle')
        } else {
            setShowError(true)
            toast.info(t('tinfo'))
        }
    }

    return (
        <div className="flex flex-col items-center gap-4">
            <div>
                <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                    {t('chapter.title')}{' '}
                    {(formData.chapterId.length <= 0 && !showError) ||
                    formData.chapterId.length > 0 ? (
                        <InfoTooltip message="Choisir un chapitre" />
                    ) : (
                        <ErrorTooltip message="Vous devrez choisir un chapitre" />
                    )}
                </div>
                <Select
                    onValueChange={value => {
                        setSelectedChapter(value)
                    }}
                >
                    <SelectTrigger className="w-[400px] max-w-full">
                        {selectedChapter ? (
                            <SelectValue
                                placeholder={t('chapter.placeholder')}
                            />
                        ) : (
                            t('chapter.placeholder')
                        )}
                    </SelectTrigger>
                    <SelectContent>
                        <SelectGroup>
                            {chapters.map(chapter => (
                                <SelectItem key={chapter.id} value={chapter.id}>
                                    {getLangProps({
                                        base: 'title',
                                        obj: chapter,
                                        lang: locale
                                    })}
                                </SelectItem>
                            ))}
                        </SelectGroup>
                    </SelectContent>
                </Select>
            </div>
            <div className="w-[400px] flex items-center justify-between gap-1">
                <p>{t('duration')}</p>
                <TimePicker date={time} setDate={value => setTime(value!)} />
            </div>
            <div className="w-full flex justify-center items-center mt-6">
                <Button
                    className="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/90 rounded-xl w-36"
                    onClick={submit}
                >
                    {t('submit')}
                </Button>
            </div>
        </div>
    )
}

export default CtrlFromDb
