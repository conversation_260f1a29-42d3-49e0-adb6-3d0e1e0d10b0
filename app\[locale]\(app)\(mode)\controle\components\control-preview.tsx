'use client'

import React, { useEffect, useRef, useState } from 'react'
import moment from 'moment'
import { ControleModeGeneratorOutput } from '@/lib/control-mode/generator/actions'
import useCtrlModeStore from '@/app/[locale]/(app)/(mode)/controle/store/controle-store'
import useAccountStore from '@/lib/stores/account-store/store'
import { useRouter } from '@/i18n/routing'
import Loading from './loading'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
    Tooltip,
    TooltipContent,
    TooltipTrigger
} from '@/components/ui/tooltip'
import { Button } from '@/components/ui/button'
import { Check, Clipboard, Printer } from 'lucide-react'
import { useReactToPrint } from 'react-to-print'
import Image from 'next/image'
import { useLocale, useTranslations } from 'next-intl'
import { getLangDir } from 'rtl-detect'
import { DesmosUi } from '@/components/desmos/DesmosUi'
import Dino<PERSON>ogo from '@/public/dinobot-logo-chat.svg'
import { toast } from 'sonner'
import { cn } from '@/lib/utils/utils'
import { MemoizedReactMarkdown } from '@/components/markdown'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeRaw from 'rehype-raw'
import rehypemathjax from 'rehype-mathjax'
import { selectUseLocalStorageStore } from '@/lib/stores/local-storage-store/local-storage.store'
import { Domain } from '@/prisma/generated/zod/modelSchema/DomainSchema'
import { getDomainById } from '@/lib/domains/actions'
import { getLangProps } from '@/lib/utils/string.utils'

type SujetControleProps = {
    getControle: (data: {
        chapterId: string
        domain: string
    }) => Promise<ControleModeGeneratorOutput[] | null>
}

// This is the left side of the control mode

const ControlPreview = ({ getControle }: SujetControleProps) => {
    const t = useTranslations('app.mode.controle.controlPreview')
    const tt = useTranslations('app.mode.train')
    const locale = useLocale()
    const dir = getLangDir(locale)
    const {
        subject,
        ctrlInfo,
        setControle,
        transformControleData,
        controle,
        timeStart,
        isRunning,
        isLoading,
        setLoding,
        reset
    } = useCtrlModeStore()
    const domainId = selectUseLocalStorageStore.use.domainId()
    const [domain, setDomain] = useState<Domain>({
        id: 0,
        name: '',
        name_en: null,
        name_ar: null,
        color: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        levelTypeId: 0,
        disabled: false
    })
    const ctrlRef = useRef<HTMLDivElement>(null)
    const { user } = useAccountStore()
    const router = useRouter()

    const getControlQuestions = async () => {
        if (domainId) {
            const dom = await getDomainById(domainId)
            if (!('error' in dom)) setDomain(dom)
        }
        if (!ctrlInfo.chapterId || !subject) {
            reset()
            router.replace('/create-controle')
        }

        setLoding(true)

        const result = await getControle({
            chapterId: ctrlInfo.chapterId,
            domain: subject!
        })

        if (result && result.length > 0) {
            setControle(transformControleData(result!, tt))
            timeStart()
        } else {
            toast.error(t('noControle'))
            reset()
            router.replace('/create-controle')
        }
        setLoding(false)
    }

    useEffect(() => {
        try {
            getControlQuestions()
        } catch (error) {
            toast.error(t('error'))
            reset()
            router.replace('/create-controle')
            console.error(error)
        }
    }, [])

    const copyControl = () => {
        let text = ''
        controle?.exercises.forEach(exo => {
            text += `${exo.title}\n`
            exo.questions.forEach((question, index) => {
                text += ` ${index + 1}. ${question.questionContent}\n`
            })
        })
        navigator.clipboard.writeText(text)
    }
    const printCtrl = useReactToPrint({
        contentRef: ctrlRef
    })

    return (
        <Tabs
            defaultValue="sujetducontrole"
            className="m-2 mx-8 xl:h-[70vh] relative"
        >
            <TabsList
                className={`flex justify-start ${dir === 'rtl' ? 'flex-row-reverse' : ''} p-0`}
            >
                <TabsTrigger
                    value="sujetducontrole"
                    className="w-44 border-dinoBotBlue border -mb-2 border-b-dinoBotBlue rounded-b-none underline text-dinoBotBlue data-[state=active]:bg-dinoBotBlue data-[state=active]:text-white"
                >
                    {t('controlSubject')}
                </TabsTrigger>
                <TabsTrigger
                    value="macopie"
                    className={`w-44 border-dinoBotBlue border -mb-2 border-b-dinoBotBlue rounded-b-none underline text-dinoBotBlue data-[state=active]:bg-dinoBotBlue data-[state=active]:text-white`}
                >
                    {t('myCopy')}
                </TabsTrigger>
                <TabsTrigger
                    value="correction"
                    className={`w-44 border-dinoBotBlue border -mb-2 border-b-dinoBotBlue rounded-b-none underline text-dinoBotBlue data-[state=active]:bg-dinoBotBlue data-[state=active]:text-white`}
                    disabled={isRunning || isLoading}
                >
                    {t('correction')}
                </TabsTrigger>
            </TabsList>
            <TabsContent
                value="sujetducontrole"
                className="m-0 p-4 xl:overflow-y-scroll h-full border border-dinoBotBlue "
            >
                <div ref={ctrlRef}>
                    {isLoading ? (
                        <Loading />
                    ) : (
                        controle?.exercises.map(answer => (
                            <div
                                key={answer.id}
                                className={`flex flex-col ${dir === 'ltr' ? '' : 'text-right'} `}
                            >
                                <h2 className="text-lg font-bold text-sky-900 ">
                                    {answer.title}
                                </h2>
                                <div className="flex flex-col m-2">
                                    {answer.questions.map(content => (
                                        <div
                                            key={content.id}
                                            className="flex flex-col my-1"
                                        >
                                            <GenerateFormatedText
                                                className="text-dinoBotGray font-semibold"
                                                content={content.id + 1 + ')'}
                                            >
                                                {content.questionContent}
                                            </GenerateFormatedText>
                                            {content.desmosCode &&
                                            content.desmosCode.expressions ? (
                                                <DesmosUi
                                                    data={content.desmosCode}
                                                    className="w-96 h-80"
                                                />
                                            ) : (
                                                ''
                                            )}
                                        </div>
                                    ))}
                                </div>
                            </div>
                        ))
                    )}
                </div>
                <div className="absolute top-[-9999px]">
                    <div ref={ctrlRef} className="p-4">
                        <div className="w-full flex gap-2 items-center justify-center py-3 bg-dinoBotVibrantBlue/5">
                            <Image
                                src={DinoLogo}
                                alt="logo"
                                width={48}
                                height={48}
                            />
                            <div className="text-2xl text-dinoBotBlue font-semibold">
                                DinoBot
                            </div>
                        </div>
                        {isLoading ? (
                            <Loading />
                        ) : (
                            controle?.exercises.map(answer => (
                                <div
                                    key={answer.id}
                                    className={`flex flex-col ${dir === 'ltr' ? '' : 'text-right'} `}
                                >
                                    <h2 className="text-lg font-bold text-sky-900 ">
                                        {answer.title}
                                    </h2>
                                    <div className="flex flex-col m-2">
                                        {answer.questions.map(content => (
                                            <div
                                                key={content.id}
                                                className="flex flex-col my-1"
                                            >
                                                <GenerateFormatedText
                                                    className="text-dinoBotGray font-semibold"
                                                    content={
                                                        content.id + 1 + ''
                                                    }
                                                >
                                                    {content.questionContent}
                                                </GenerateFormatedText>
                                                {content.desmosCode &&
                                                content.desmosCode
                                                    .expressions ? (
                                                    <DesmosUi
                                                        data={
                                                            content.desmosCode
                                                        }
                                                        className="w-96 h-80"
                                                        style={{
                                                            pageBreakInside:
                                                                'avoid'
                                                        }}
                                                    />
                                                ) : (
                                                    ''
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </div>
            </TabsContent>
            <CtrlOptions onCopy={copyControl} onPrint={printCtrl} />
            <TabsContent
                value="macopie"
                className="m-0 p-8 overflow-y-scroll h-full border border-dinoBotBlue"
            >
                <div
                    className={`border p-2 ${dir === 'ltr' ? '' : 'text-right'} `}
                    ref={ctrlRef}
                >
                    <div className="flex justify-between">
                        <h3>
                            {user.firstName} {user.lastName}
                        </h3>
                        <h3>{moment().format('DD/MM/yyyy')}</h3>
                    </div>
                    <div className="flex flex-col items-center">
                        <h2 className="underline">
                            {t('controlOf') +
                                ' ' +
                                getLangProps({
                                    obj: domain!,
                                    base: 'name',
                                    lang: locale
                                })}
                        </h2>
                    </div>
                    <div className="mt-5 ml-5">
                        {isLoading ? (
                            <Loading />
                        ) : (
                            controle?.exercises.map(answer => (
                                <div key={answer.id} className="flex flex-col ">
                                    <h2 className="text-lg font-bold text-sky-900">
                                        {answer.title}
                                    </h2>
                                    <div className="flex flex-col m-5">
                                        {answer.questions.map(content => (
                                            <div
                                                key={content.id}
                                                className="flex flex-col my-1"
                                            >
                                                <GenerateFormatedText className="text-dinoBotGray font-semibold">
                                                    {content.id +
                                                        1 +
                                                        ')' +
                                                        content.questionContent}
                                                </GenerateFormatedText>
                                                {content.desmosCode &&
                                                content.desmosCode
                                                    .expressions ? (
                                                    <DesmosUi
                                                        data={
                                                            content.desmosCode
                                                        }
                                                        className="w-96 h-80"
                                                    />
                                                ) : (
                                                    ''
                                                )}
                                                {content.answer ? (
                                                    <MemoizedReactMarkdown
                                                        remarkPlugins={[
                                                            remarkGfm,
                                                            remarkMath
                                                        ]}
                                                        rehypePlugins={[
                                                            rehypeRaw,
                                                            rehypemathjax
                                                        ]}
                                                        components={{
                                                            p({ children }) {
                                                                return (
                                                                    <p
                                                                        className={cn(
                                                                            'text-dinoBotGray font-semibold mt-2 mb-4 text-left',
                                                                            'mb-2 last:mb-0'
                                                                        )}
                                                                    >
                                                                        {
                                                                            children
                                                                        }
                                                                    </p>
                                                                )
                                                            }
                                                        }}
                                                    >
                                                        {content.answer}
                                                    </MemoizedReactMarkdown>
                                                ) : (
                                                    <div className="text-red-600 p-2 rounded-lg">
                                                        {t('noAnswer')}
                                                    </div>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </div>
                <div className="absolute top-[-9999px]">
                    <div
                        className={`border p-2 ${dir === 'ltr' ? '' : 'text-right'} `}
                        ref={ctrlRef}
                    >
                        <div className="flex justify-between">
                            <h3>
                                {user.firstName} {user.lastName}
                            </h3>
                            <h3>{moment().format('DD/MM/yyyy')}</h3>
                        </div>
                        <div className="flex flex-col items-center">
                            <h2 className="underline">
                                {t('controlOf') +
                                    ' ' +
                                    getLangProps({
                                        obj: domain!,
                                        base: 'name',
                                        lang: locale
                                    })}
                            </h2>
                        </div>
                        <div className="mt-5 ml-5">
                            {isLoading ? (
                                <Loading />
                            ) : (
                                controle?.exercises.map(answer => (
                                    <div
                                        key={answer.id}
                                        className="flex flex-col "
                                    >
                                        <h2 className="text-lg font-bold text-sky-900">
                                            {answer.title}
                                        </h2>
                                        <div className="flex flex-col m-5">
                                            {answer.questions.map(content => (
                                                <div
                                                    key={content.id}
                                                    className="flex flex-col my-1"
                                                >
                                                    <GenerateFormatedText
                                                        className="text-dinoBotGray font-semibold"
                                                        content={
                                                            content.id + 1 + ')'
                                                        }
                                                    >
                                                        {
                                                            content.questionContent
                                                        }
                                                    </GenerateFormatedText>
                                                    {content.desmosCode &&
                                                    content.desmosCode
                                                        .expressions ? (
                                                        <DesmosUi
                                                            data={
                                                                content.desmosCode
                                                            }
                                                            className="w-96 h-80"
                                                            style={{
                                                                pageBreakInside:
                                                                    'avoid'
                                                            }}
                                                        />
                                                    ) : (
                                                        ''
                                                    )}
                                                    {content.answer ? (
                                                        <MemoizedReactMarkdown
                                                            remarkPlugins={[
                                                                remarkGfm,
                                                                remarkMath
                                                            ]}
                                                            rehypePlugins={[
                                                                rehypeRaw,
                                                                rehypemathjax
                                                            ]}
                                                            components={{
                                                                p({
                                                                    children
                                                                }) {
                                                                    return (
                                                                        <p
                                                                            className={cn(
                                                                                'text-dinoBotGray font-semibold mt-2 mb-4 text-left',
                                                                                'mb-2 last:mb-0'
                                                                            )}
                                                                        >
                                                                            {
                                                                                children
                                                                            }
                                                                        </p>
                                                                    )
                                                                }
                                                            }}
                                                        >
                                                            {content.answer}
                                                        </MemoizedReactMarkdown>
                                                    ) : (
                                                        <div className="text-red-600 p-2 rounded-lg">
                                                            {t('noAnswer')}
                                                        </div>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>
                    </div>
                </div>
            </TabsContent>
            <TabsContent
                value="correction"
                className="m-0 p-8 overflow-y-scroll h-full border border-dinoBotBlue"
            >
                <div
                    className={`border p-2 ${dir === 'ltr' ? '' : 'text-right'} `}
                    ref={ctrlRef}
                >
                    <div className="flex justify-between">
                        <h3>
                            {user.firstName} {user.lastName}
                        </h3>
                        <h3>{moment().format('DD/MM/yyyy')}</h3>
                    </div>
                    <div className="flex flex-col items-center">
                        <h2 className="underline">
                            {t('controlOf') +
                                ' ' +
                                getLangProps({
                                    obj: domain!,
                                    base: 'name',
                                    lang: locale
                                })}
                        </h2>
                    </div>
                    <div className="mt-5 ml-5">
                        {isLoading ? (
                            <Loading />
                        ) : (
                            controle?.exercises.map(answer => (
                                <div key={answer.id} className="flex flex-col ">
                                    <h2 className="text-lg font-bold text-sky-900">
                                        {answer.title}
                                    </h2>
                                    <div className="flex flex-col m-5">
                                        {answer.questions.map(content => (
                                            <div
                                                key={content.id}
                                                className="flex flex-col my-1"
                                            >
                                                <GenerateFormatedText className="text-dinoBotGray font-semibold">
                                                    {content.id +
                                                        1 +
                                                        ')' +
                                                        content.questionContent}
                                                </GenerateFormatedText>
                                                {content.desmosCode &&
                                                content.desmosCode
                                                    .expressions ? (
                                                    <DesmosUi
                                                        data={
                                                            content.desmosCode
                                                        }
                                                        className="w-96 h-80"
                                                    />
                                                ) : (
                                                    ''
                                                )}
                                                {content.feedback && (
                                                    <GenerateFormatedText className="text-red-600 p-2 rounded-lg">
                                                        {content.feedback}
                                                    </GenerateFormatedText>
                                                )}
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </div>
                <div className="absolute top-[-9999px]">
                    <div
                        className={`border p-2 ${dir === 'ltr' ? '' : 'text-right'} `}
                        ref={ctrlRef}
                    >
                        <div className="flex justify-between">
                            <h3>
                                {user.firstName} {user.lastName}
                            </h3>
                            <h3>{moment().format('DD/MM/yyyy')}</h3>
                        </div>
                        <div className="flex flex-col items-center">
                            <h2 className="underline">
                                {t('controlOf') +
                                    ' ' +
                                    getLangProps({
                                        obj: domain!,
                                        base: 'name',
                                        lang: locale
                                    })}
                            </h2>
                        </div>
                        <div className="mt-5 ml-5">
                            {isLoading ? (
                                <Loading />
                            ) : (
                                controle?.exercises.map(answer => (
                                    <div
                                        key={answer.id}
                                        className="flex flex-col "
                                    >
                                        <h2 className="text-lg font-bold text-sky-900">
                                            {answer.title}
                                        </h2>
                                        <div className="flex flex-col m-5">
                                            {answer.questions.map(content => (
                                                <div
                                                    key={content.id}
                                                    className="flex flex-col my-1"
                                                >
                                                    <GenerateFormatedText
                                                        className="text-dinoBotGray font-semibold"
                                                        content={
                                                            content.id + 1 + ')'
                                                        }
                                                    >
                                                        {
                                                            content.questionContent
                                                        }
                                                    </GenerateFormatedText>
                                                    {content.desmosCode &&
                                                    content.desmosCode
                                                        .expressions ? (
                                                        <DesmosUi
                                                            data={
                                                                content.desmosCode
                                                            }
                                                            className="w-96 h-80"
                                                            style={{
                                                                pageBreakInside:
                                                                    'avoid'
                                                            }}
                                                        />
                                                    ) : (
                                                        ''
                                                    )}

                                                    {content.feedback && (
                                                        <GenerateFormatedText className="text-red-600 p-2 rounded-lg">
                                                            {content.feedback}
                                                        </GenerateFormatedText>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>
                    </div>
                </div>
            </TabsContent>
        </Tabs>
    )
}

export default ControlPreview

const GenerateFormatedText = ({
    children,
    className,
    content = ''
}: {
    children: string
    className: string
    content?: string
}) => {
    return (
        <MemoizedReactMarkdown
            remarkPlugins={[remarkGfm, remarkMath]}
            rehypePlugins={[rehypeRaw, rehypemathjax]}
            components={{
                p({ children }) {
                    return (
                        <p className={cn(className, 'mb-2 last:mb-0')}>
                            {children}
                        </p>
                    )
                }
            }}
        >
            {`${content} ${children}`}
        </MemoizedReactMarkdown>
    )
}
interface CtrlOptionsProps {
    onCopy?: () => void
    onGenerate?: () => void
    onPrint?: () => void
    onDownload?: () => void
}

function CtrlOptions({ onCopy, onPrint }: CtrlOptionsProps) {
    const t = useTranslations('app.mode.controle.controlPreview')
    const [animGenerate, setAnimGenerate] = useState(false)
    const [animCopy, setAnimCopy] = useState(false)
    const [animDownload, setAnimDownload] = useState(false)

    const locale = useLocale()
    const dir = getLangDir(locale)
    useEffect(() => {
        if (animGenerate) setTimeout(() => setAnimGenerate(false), 1500)
        if (animCopy) setTimeout(() => setAnimCopy(false), 1500)
        if (animDownload) setTimeout(() => setAnimDownload(false), 1500)
    }, [animGenerate, animCopy, animDownload])

    return (
        <div
            className={`flex gap-2 absolute -bottom-16 ${dir === 'ltr' ? 'right-0' : 'left-0'} `}
        >
            <Tooltip>
                <TooltipTrigger asChild>
                    <Button
                        variant="ghost"
                        className="size-5 p-0 hover:bg-background"
                        onClick={
                            onCopy
                                ? () => {
                                      setAnimCopy(true)
                                      onCopy()
                                  }
                                : undefined
                        }
                    >
                        {!animCopy ? (
                            <Clipboard />
                        ) : (
                            <Check className="animate-fade-in" />
                        )}
                        <span className="sr-only">Copier L&apos;exercice</span>
                    </Button>
                </TooltipTrigger>
                <TooltipContent className="bg-dinoBotBlue">
                    {t('copyExercise')}
                </TooltipContent>
            </Tooltip>

            <Tooltip>
                <TooltipTrigger asChild>
                    <Button
                        variant="ghost"
                        className="size-5 p-0 hover:bg-background"
                        onClick={onPrint ?? undefined}
                    >
                        <Printer />
                        <span className="sr-only">
                            Imprimer L&apos;exercice
                        </span>
                    </Button>
                </TooltipTrigger>
                <TooltipContent className="bg-dinoBotBlue">
                    {t('printExercise')}
                </TooltipContent>
            </Tooltip>
        </div>
    )
}
