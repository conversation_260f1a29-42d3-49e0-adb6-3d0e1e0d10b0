'use client'
import { ScrollArea } from '@/components/ui/scroll-area'
import React, { useEffect } from 'react'
import CourseCard from './course-card'
import { InfoIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import CourseDialog from './course-dialog'
import { getStudentClasses } from '@/lib/control-mode/services/class/actions'
import { ClassWithPartialRelations } from '@/prisma/generated/zod/modelSchema/ClassSchema'
import { useTranslations } from 'next-intl'

function CoursesList() {
    const [courses, setCourses] = React.useState<ClassWithPartialRelations[]>(
        []
    )

    const translate = useTranslations('app.courses.index')

    useEffect(() => {
        ;(async () => {
            const response = await getStudentClasses()
            setCourses(response as ClassWithPartialRelations[])
            console.log('Classes', response)
        })()
    }, [])

    return (
        <ScrollArea className="h-3/4 w-11/12 bg-white border-dinoBotWhite border p-4 m-4 rounded-sm">
            <div className="flex justify-between">
                <div className="flex gap-2">
                    <h2 className="font-bold">{translate('title')}</h2>
                    <InfoIcon />
                </div>
                <CourseDialog
                    setCourses={setCourses}
                    trigger={
                        <Button
                            variant="link"
                            className="text-dinoBotBlue underline"
                        >
                            {translate('dialog.trigger')}
                        </Button>
                    }
                />
            </div>

            <div className="flex flex-wrap mt-2">
                {courses &&
                    courses.map((item, index) => (
                        <CourseCard key={index} {...item} />
                    ))}
            </div>
        </ScrollArea>
    )
}

export default CoursesList
