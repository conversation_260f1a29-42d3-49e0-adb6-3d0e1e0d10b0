import { getNotationByClassIdAndStudentId } from '@/lib/evaluation-scheduler/actions'
import { createSelectors } from '@/lib/stores/selectors.store'
import { PlannedEvaluationPartialWithRelations } from '@/prisma/generated/zod/modelSchema/PlannedEvaluationSchema'
import { create } from 'zustand'

type NotationStoreType = NotationStateType & NotationActionType
type NotationStateType = {
    plannedEvaluations: PlannedEvaluationPartialWithRelations[]
    selectedPlannedEvaluations: PlannedEvaluationPartialWithRelations | null
}
type NotationActionType = {
    setPlannedEvaluations: (
        plannedEvaluations: PlannedEvaluationPartialWithRelations[]
    ) => void
    setSelectedPlannedEvaluations: (
        plannedEvaluation: PlannedEvaluationPartialWithRelations
    ) => void
    fetchNotation: (courseId: string, order?: 'asc') => void
}
const notationStateInitial: NotationStateType = {
    plannedEvaluations: [],
    selectedPlannedEvaluations: null
}

export const useNotationStore = create<NotationStoreType>()(set => ({
    ...notationStateInitial,
    setPlannedEvaluations: (
        plannedEvaluations: PlannedEvaluationPartialWithRelations[]
    ) => set({ plannedEvaluations }),
    setSelectedPlannedEvaluations: (
        selectedPlannedEvaluations: PlannedEvaluationPartialWithRelations
    ) => set({ selectedPlannedEvaluations }),
    fetchNotation: async (courseId, order) => {
        try {
            const data = await getNotationByClassIdAndStudentId(courseId, order)
            if ('error' in data) throw new Error(data.error)
            set({
                plannedEvaluations:
                    data as PlannedEvaluationPartialWithRelations[]
            })
        } catch (error) {
            console.error(error)
        }
    }
}))

export const selectorNotationStore = createSelectors(useNotationStore)
