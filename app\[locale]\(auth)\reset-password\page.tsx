import { auth } from '@/auth'
import InitResetPasswordForm from '@/components/reset-pwd/init-reset-password-form'
import { Session } from '@/lib/types'
import { redirect } from '@/i18n/routing'
import { getLocale } from 'next-intl/server'
import React from 'react'

export default async function ResetPasswordPage() {
    const session = (await auth()) as Session
    const locale = await getLocale()

    if (session) {
        redirect({ href: '/', locale })
    }

    return (
        <main className="flex flex-col size-full">
            <InitResetPasswordForm />
        </main>
    )
}
