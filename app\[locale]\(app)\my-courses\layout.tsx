import React from 'react'
import StudentSidebar from '../components/student-sidebar'

type myCoursesLayout = {
    children: React.ReactNode
}
async function myCoursesLayout({ children }: myCoursesLayout) {
    return (
        <div className="flex flex-row w-screen h-screen overflow-hidden bg-[#FAFAFA]">
            <div className="w-fit">
                <StudentSidebar />
            </div>
            <div className="grow flex flex-col relative">{children}</div>
        </div>
    )
}

export default myCoursesLayout
