'use client'

import React from 'react'
import { useTimer } from 'react-timer-hook'
import Digit from './Digit'
import useCtrlModeStore, {
    selectUseCtrlModeStore
} from '@/app/[locale]/(app)/(mode)/controle/store/controle-store'
import { useEffect, useRef } from 'react'
import { useTranslations } from 'next-intl'

const Timer = () => {
    const { letstart, timeHidden, setTimeHidden } = useCtrlModeStore()
    const selectedTime = selectUseCtrlModeStore.use.time()
    const time = new Date()
    time.setHours(
        time.getHours() + selectedTime.getHours(),
        time.getMinutes() + selectedTime.getMinutes(),
        time.getSeconds() + selectedTime.getSeconds()
    )
    const { hours, minutes, seconds, start, isRunning, pause } = useTimer({
        expiryTimestamp: time,
        onExpire: () => console.warn('onExpire called'),
        autoStart: false
    })
    const t = useTranslations('app.mode.controle')
    const flag = useRef(false)
    useEffect(() => {
        letstart(start, isRunning, pause)
        if (isRunning && !flag.current) {
            setTimeHidden(false)
            flag.current = true
        }
        if (!isRunning && flag.current) {
            setTimeHidden(true)
        }
    }, [isRunning])
    return (
        <div
            className={`flex flex-col py-2 px-4 items-center  bg-white text-dinoBotRed border-gray-400 border-2 rounded-xl  ${timeHidden ? 'hidden' : ''} `}
        >
            <p>{t('timer')}</p>
            <p className="bg-dinoBotRed text-white w-4/5 rounded-lg text-center">
                <Digit value={hours} />:<Digit value={minutes} />:
                <Digit value={seconds} />
            </p>
        </div>
    )
}

export default Timer
