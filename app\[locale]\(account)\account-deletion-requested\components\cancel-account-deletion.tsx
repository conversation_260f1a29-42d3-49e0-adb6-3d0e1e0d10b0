'use client'

import { useReducer } from 'react'
import { Button } from '@/components/ui/button'
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { toast } from 'sonner'
import { useTranslations } from 'next-intl'
import { useRouter } from 'next/navigation'
import { useMutation, useQuery } from '@tanstack/react-query'
import {
    disconnect,
    enableAccount,
    getDeletionDate
} from '@/lib/account/actions'
import { cn } from '@/lib/utils/utils'
import moment from 'moment'

const initialState = {
    showPasswordDialog: false,
    password: '',
    isSubmitting: false, // Kept for potential future use with verifyPassword...
    passwordError: ''
}

type State = typeof initialState

type Action =
    | { type: 'SHOW_PASSWORD_DIALOG'; payload: boolean }
    | { type: 'SET_PASSWORD'; payload: string }
    | { type: 'SET_SUBMITTING'; payload: boolean }
    | { type: 'SET_PASSWORD_ERROR'; payload: string }
    | { type: 'RESET_FORM' }

function reducer(state: State, action: Action): State {
    switch (action.type) {
        case 'SHOW_PASSWORD_DIALOG':
            return {
                ...state,
                showPasswordDialog: action.payload,
                password: '',
                passwordError: ''
            }
        case 'SET_PASSWORD':
            return { ...state, password: action.payload, passwordError: '' }
        case 'SET_SUBMITTING':
            return { ...state, isSubmitting: action.payload }
        case 'SET_PASSWORD_ERROR':
            return { ...state, passwordError: action.payload }
        case 'RESET_FORM':
            return {
                ...initialState
            }
        default:
            return state
    }
}

type CancelAccountDeletionProps = {
    email: string
}
export default function CancelAccountDeletion({
    email
}: CancelAccountDeletionProps) {
    const t = useTranslations('app.chat.profile.delete-account')
    const t_dialog = useTranslations('app.chat.account.cancelDeletionDialog') // New namespace for dialog text
    const router = useRouter()

    moment.locale(t('locale'))

    let formattedDate = ''
    let formattedTime = ''

    const [state, dispatch] = useReducer(reducer, initialState)

    const activateAccount = useMutation({
        mutationFn: async (password: string) => {
            return await enableAccount(password) // Ensure this returns the object { error?, success? }
        },
        onSuccess: data => {
            // Assuming enableAccount returns an object like { error?: string, success?: boolean }
            if (data?.error) {
                // Use the error key from data for translation
                const errorMessage = t(data.error)
                dispatch({ type: 'SET_PASSWORD_ERROR', payload: errorMessage })
                toast.error(t(errorMessage))
                // Do not reset form or redirect on error
            } else {
                // Success case
                router.push('/')
                toast.success(t_dialog('success.accountReactivated'))
                dispatch({ type: 'RESET_FORM' })
            }
        },
        onError: (error: any) => {
            // This catches network errors or exceptions from enableAccount if it throws them
            // instead of returning an error object.
            const errorMessage = t_dialog('error.generic') // Fallback generic error
            dispatch({ type: 'SET_PASSWORD_ERROR', payload: errorMessage })
            toast.error(errorMessage)
        }
    })

    const { data: date } = useQuery({
        queryKey: ['deletionDate'],
        queryFn: async () => await getDeletionDate()
    })

    if (date instanceof Date) {
        const day = moment(date!)
        formattedDate = day.format('DD MMMM YYYY')
        formattedTime = day.format('HH:mm')
    }

    const handleOpenPasswordDialog = () => {
        dispatch({ type: 'SHOW_PASSWORD_DIALOG', payload: true })
    }

    const handlePasswordSubmit = async () => {
        if (!state.password) {
            dispatch({
                type: 'SET_PASSWORD_ERROR',
                payload: t_dialog('passwordRequired')
            })
            return
        }
        dispatch({ type: 'SET_SUBMITTING', payload: true })

        try {
            // const response = await verifyPasswordAndReactivateAccount(state.password) // Server action to verify password
            // if (response.error) {
            //     dispatch({ type: 'SET_PASSWORD_ERROR', payload: t_dialog(response.error) })
            //     toast.error(t_dialog(response.error) || t_dialog('error.generic'))
            // } else {
            //     activateAccount.mutate() // This will trigger navigation and toast via its onSuccess
            // }
            // For now, directly call activateAccount.mutate as password verification is not implemented
            activateAccount.mutate(state.password)
        } catch (error) {
            console.error('Error reactivating account:', error)
            dispatch({
                type: 'SET_PASSWORD_ERROR',
                payload: t_dialog('error.generic')
            })
            toast.error(t_dialog('error.generic'))
        } finally {
            // SET_SUBMITTING to false is handled by activateAccount.mutate's onSettled or if not using it, here.
            // If verifyPasswordAndReactivateAccount is used, SET_SUBMITTING should be in its finally block.
            // For now, as activateAccount handles its own state for pending, we might not need state.isSubmitting for the button directly.
            // However, if verifyPassword... has its own async logic before activateAccount.mutate, then state.isSubmitting is useful.
            // Let's assume the button's disabled state will primarily rely on activateAccount.isPending and state.password for validation.
            // We can remove state.isSubmitting if verifyPasswordAndReactivateAccount is not used or handles its own submission state.
            // For simplicity, let's reset it here if direct mutation is the only path.
            if (!activateAccount.isPending) {
                // Only set submitting to false if mutation isn't running
                dispatch({ type: 'SET_SUBMITTING', payload: false })
            }
        }
    }

    const handleLogout = () => {
        disconnect()
    }

    return (
        <div className="flex flex-col items-center justify-center min-h-screen p-4">
            <div className="max-w-xl w-full bg-card p-8 rounded-lg shadow-2xl flex flex-col items-center">
                <img src="/dinobot-logo.svg" alt="Logo" className="w-60 my-2" />
                <h1 className="text-2xl font-bold mb-6 text-center">
                    {t('deletion-requested-title')}
                </h1>

                <div className="space-y-4 text-center mb-8">
                    <p>{t('deletion-requested-message')}</p>

                    <p className="font-medium">
                        {t('account-suspended-message', {
                            date: formattedDate,
                            time: formattedTime
                        })}
                    </p>

                    <p>
                        {t('confirmation-email-sent', {
                            email: email || ''
                        })}
                    </p>

                    <p className="pt-4">{t('cancellation-instructions')}</p>
                </div>

                <footer className="flex justify-center gap-2">
                    <Button
                        variant="destructive"
                        className="w-full"
                        onClick={handleLogout}
                    >
                        {t('logoutButton')}
                    </Button>
                    <Button
                        variant="default"
                        className={cn(
                            'w-full bg-dinoBotBlue hover:bg-dinoBotBlue/80',
                            activateAccount.isPending && 'cursor-not-allowed'
                        )}
                        onClick={handleOpenPasswordDialog} // Changed to open dialog
                    >
                        {activateAccount.isPending
                            ? t('loading')
                            : t('cancel-deletion-button')}{' '}
                        {/* Changed key */}
                    </Button>
                </footer>
            </div>

            {/* Password Dialog */}
            <Dialog
                open={state.showPasswordDialog}
                onOpenChange={isOpen =>
                    dispatch({ type: 'SHOW_PASSWORD_DIALOG', payload: isOpen })
                }
            >
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>{t_dialog('title')}</DialogTitle>
                        <DialogDescription className="pt-2">
                            {t_dialog('description')}
                        </DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <Input
                            id="password"
                            type="password"
                            placeholder={t_dialog('passwordPlaceholder')}
                            value={state.password}
                            onChange={e =>
                                dispatch({
                                    type: 'SET_PASSWORD',
                                    payload: e.target.value
                                })
                            }
                            className="col-span-3"
                        />
                        {state.passwordError && (
                            <p className="text-sm text-red-500">
                                {state.passwordError}
                            </p>
                        )}
                    </div>
                    <DialogFooter>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() =>
                                dispatch({
                                    type: 'SHOW_PASSWORD_DIALOG',
                                    payload: false
                                })
                            }
                        >
                            {t_dialog('cancelButton')}
                        </Button>
                        <Button
                            type="button"
                            onClick={handlePasswordSubmit}
                            disabled={
                                activateAccount.isPending || !state.password
                            } // Button disabled if pending or no password
                            className={cn(
                                'bg-dinoBotBlue hover:bg-dinoBotBlue/80',
                                (activateAccount.isPending ||
                                    !state.password) &&
                                    'cursor-not-allowed'
                            )}
                        >
                            {activateAccount.isPending
                                ? t('loading')
                                : t_dialog('confirmButton')}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
}
