import { getPartsByChapter } from '@/lib/training-mode/server'
import { getChapters } from '@/lib/chapters/actions'
import CtrlModeLayout from './components/ctrl-mode-layout'
import React from 'react'
import { getLocale } from 'next-intl/server'
import { getFeatureRoutesByFeatureName } from '@/lib/features/actions'
import { FeatureFlagName } from '@prisma/client'
import { notFound } from 'next/navigation'
import { redirect } from '@/i18n/routing'

const CreateControle = async () => {
    const locale = await getLocale()
    const featureFlags = await getFeatureRoutesByFeatureName(
        FeatureFlagName.STUDENT_EVALUATION_MODE
    )

    if (Array.isArray(featureFlags) && featureFlags[0] != null) {
        if (featureFlags[0] === 'not-found') notFound()
        redirect({ href: `/${featureFlags[0]}`, locale })
    }
    return (
        <div className="size-full">
            <CtrlModeLayout
                getChapters={getChapters}
                getParts={getPartsByChapter}
            />
        </div>
    )
}

export default CreateControle
