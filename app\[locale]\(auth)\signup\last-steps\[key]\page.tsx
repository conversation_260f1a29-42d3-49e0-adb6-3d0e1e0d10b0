import SignupLastStepsForm from '@/components/signup/sign-up-last-steps-form'
import { notFound } from 'next/navigation'
import { checkIfKeyIsValid } from './actions'
import React from 'react'
import InvalidVerifKey from './invalid-key'
import { redirect } from '@/i18n/routing'
import { getLocale } from 'next-intl/server'

export interface SignUpLastStepsPageProps {
    params: Promise<{
        key: string
    }>
}

export default async function SignupLastStepsPage(
    props: SignUpLastStepsPageProps
) {
    const params = await props.params
    const locale = await getLocale()
    //const session = (await auth()) as Session

    const isKeyValid = await checkIfKeyIsValid(params.key)
    // console.log(isKeyValid)
    // if (session) {
    //   redirect('/')
    // }

    if (!params.key) {
        notFound()
    }

    if (process.env.REGISTRATION_TYPE === 'none') {
        redirect({ href: '/login', locale })
    }

    return (
        <main className="flex flex-col size-full">
            {isKeyValid ? (
                <SignupLastStepsForm
                    verificationKey={params.key}
                    isTeacher={isKeyValid.user?.type === 'teacher'}
                />
            ) : (
                <InvalidVerifKey />
            )}
        </main>
    )
}
