import { Check, Download, Loader, RotateCcw } from 'lucide-react'
import { selectUseCtrlMediaStore } from '../../../store/controle-media-store'
import ImageViewer from './image-viewer'
import VideoPlayer from './video-player'
import { ControlQuestionMedia } from '@/lib/control-mode/types/control/types'
import { getControlMedia } from '@/lib/control-mode/actions'
import { useState } from 'react'
import { toast } from 'sonner'
import { useTranslations } from 'next-intl'

interface Props {
    file: ControlQuestionMedia
    onClick: () => void
}

function Asset({ file, onClick }: Props) {
    const t = useTranslations('app.mode.controle.controlPreview')

    const [isDownloading, setIsDownloading] = useState<
        'progress' | 'done' | 'none' | 'error'
    >('none')

    async function handleDownload(key: string, customFilename?: string) {
        try {
            setIsDownloading('progress')
            const file = await getControlMedia(key)
            if (!file) {
                throw new Error(t('failedToDownload'))
            }

            const blobUrl = URL.createObjectURL(file)
            const filename =
                customFilename || key.split('/').pop() || 'download'

            const a = document.createElement('a')
            a.href = blobUrl
            a.download = filename
            a.style.display = 'none'

            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)

            URL.revokeObjectURL(blobUrl)
            setIsDownloading('done')
        } catch (error: any) {
            setIsDownloading('error')
            console.error('Failed download error:', error)
            toast.error(error.message)
        } finally {
            setTimeout(() => {
                setIsDownloading('none')
            }, 2000)
        }
    }

    return (
        <div
            onClick={onClick}
            className={`group relative min-w-[200px] max-w-[200px]  p-1 rounded  cursor-pointer  hover:bg-dinoBotVibrantBlue/10 `}
        >
            <button
                onClick={e => {
                    e.stopPropagation()
                    handleDownload(file.fileUrl!)
                }}
                className="absolute top-1.5 right-1.5 p-1 z-50 bg-dinoBotWhite/40 group-hover:block hidden hover:bg-dinoBotWhite transition-colors duration-200 rounded"
            >
                <Download />
            </button>
            {isDownloading != 'none' ? (
                <div className="size-full bg-black/10 absolute top-0 left-0 flex justify-center items-center">
                    {isDownloading === 'progress' ? (
                        <Loader className="animate-spin transition-all duration-[1500] size-8 text-dinoBotVibrantBlue" />
                    ) : isDownloading === 'done' ? (
                        <div className="size-10 bg-dinoBotGreen rounded-full flex justify-center items-center text-dinoBotWhite">
                            <Check className="size-8" />
                        </div>
                    ) : isDownloading === 'error' ? (
                        <button
                            onClick={e => {
                                e.stopPropagation()
                                handleDownload(file.fileUrl!)
                            }}
                            className="size-10 bg-dinoBotRed hover:bg-dinoBotRed/80 rounded-full flex justify-center items-center text-dinoBotWhite"
                        >
                            <RotateCcw className="size-6" />
                        </button>
                    ) : null}
                </div>
            ) : null}
            {file.fileType?.includes('image') ? (
                <ImageViewer url={file.signedUrl!} alt={file.fileName!} />
            ) : file.fileType?.includes('video') ? (
                <VideoPlayer url={file.signedUrl!} />
            ) : null}
        </div>
    )
}

export default Asset
