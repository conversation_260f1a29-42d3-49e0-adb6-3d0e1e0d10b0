'use client'
import { useMutation, useQuery } from '@tanstack/react-query'
import React, { useEffect, useState } from 'react'
import { selectUseContentStore } from '../../store/content.store'
import {
    getChaptersWithPartsAndFilteredTasks,
    updateChapterActiveStatus
} from '@/lib/praxeo/actions'
import { MatchQuery } from '@/components/match-query/match-query'
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger
} from '@/components/ui/accordion'
import { selectTasks } from '../store/tasks.store'
import PartsList from './parts-list'
import IconeSwitch from '@/components/shared/ui/icone-switch'
import { Eye, EyeOff, LoaderCircle } from 'lucide-react'
import { toast } from 'sonner'

const ChapterList = () => {
    const domainId = selectUseContentStore.use.domainId()
    const levelId = selectUseContentStore.use.levelId()
    const searchFilter = selectTasks.use.searchFilter()
    const { setChaptersWithData } = selectTasks.use.actions()
    const [openItems, setOpenItems] = useState<string[]>([])

    const chapterQuery = useQuery({
        queryKey: ['chapters-with-tasks', domainId, levelId, searchFilter],
        queryFn: async () =>
            getChaptersWithPartsAndFilteredTasks(
                domainId!,
                levelId!,
                searchFilter || undefined
            ),
        enabled: !!domainId && !!levelId
    })

    useEffect(() => {
        if (chapterQuery.data) {
            setChaptersWithData(chapterQuery.data)
        }
    }, [chapterQuery.data, setChaptersWithData])

    const updateChapterMutation = useMutation({
        mutationFn: ({
            chapterId,
            isDisabled
        }: {
            chapterId: string
            isDisabled: boolean
        }) => updateChapterActiveStatus(chapterId, isDisabled),
        onSuccess: () => {
            chapterQuery.refetch()
        },
        onError: (error: any) =>
            toast.error('Erreur lors de la mise à jour: ' + error.message)
    })

    const handleToggleActive = (id: string, disabled: boolean) => {
        const newStatus = !disabled // Toggle the disabled status

        // If disabling the chapter (newStatus is true), remove it from openItems to close the accordion
        if (newStatus) {
            setOpenItems(prev => prev.filter(item => item !== id))
        }

        updateChapterMutation.mutate({ chapterId: id, isDisabled: newStatus })
    }

    return (
        <MatchQuery
            value={chapterQuery}
            success={chaptersWithData => {
                // Filtrer les chapitres qui ont au moins 1 tâche
                const chaptersWithTasks =
                    chaptersWithData?.filter(chapter => {
                        const totalTasks = chapter.parts.reduce(
                            (acc, part) => acc + part.tasks.length,
                            0
                        )
                        return totalTasks > 0
                    }) || []

                if (chaptersWithTasks.length === 0) {
                    return (
                        <div className="text-center text-gray-500">
                            Aucun chapitre avec des tâches disponible
                        </div>
                    )
                }

                return (
                    <Accordion
                        value={openItems}
                        onValueChange={setOpenItems}
                        type="multiple"
                        className="flex flex-col gap-2"
                    >
                        {chaptersWithTasks.map(chapter => (
                            <AccordionItem
                                key={chapter.id}
                                value={chapter.id}
                                disabled={chapter.disabled}
                                className={`border-b-0 ${chapter.disabled ? 'opacity-50' : ''}`}
                            >
                                <div className="relative">
                                    <AccordionTrigger
                                        className={`rounded-md px-2 ${chapter.disabled ? 'bg-dinoBotLightGray/100' : 'bg-dinoBotLightGray/45'}`}
                                        iconClassName="size-6"
                                    >
                                        <div className="w-full flex items-center justify-start gap-1">
                                            <span className="font-semibold">
                                                Chapitre :
                                            </span>{' '}
                                            {chapter.title}
                                            <span className="text-sm text-gray-500">
                                                (
                                                {chapter.parts.reduce(
                                                    (acc, part) =>
                                                        acc + part.tasks.length,
                                                    0
                                                )}{' '}
                                                tâches)
                                            </span>
                                        </div>
                                    </AccordionTrigger>
                                    <div
                                        className="absolute right-4 top-1/2 -translate-y-1/2 z-10" // Added z-10 to ensure it's above the AccordionTrigger
                                        onClick={e => {
                                            e.stopPropagation() // Prevent event propagation to the AccordionTrigger
                                            handleToggleActive(
                                                chapter.id,
                                                chapter.disabled
                                            )
                                        }}
                                    >
                                        {updateChapterMutation.isPending &&
                                        updateChapterMutation.variables
                                            ?.chapterId === chapter.id ? (
                                            <LoaderCircle className="h-5 w-5 animate-spin mr-6" />
                                        ) : (
                                            <IconeSwitch
                                                className="mr-6 cursor-pointer"
                                                isActive={
                                                    updateChapterMutation
                                                        .variables
                                                        ?.chapterId ===
                                                    chapter.id
                                                        ? updateChapterMutation
                                                              .variables
                                                              .isDisabled
                                                        : (chapter.disabled ??
                                                          true)
                                                }
                                                iconEnable={<EyeOff />}
                                                iconDisable={<Eye />}
                                            />
                                        )}
                                    </div>
                                </div>
                                <AccordionContent className="px-3">
                                    <PartsList chapter={chapter} />
                                </AccordionContent>
                            </AccordionItem>
                        ))}
                    </Accordion>
                )
            }}
            loading={() => (
                <div className="text-center">Chargement des chapitre...</div>
            )}
            error={error => (
                <div className="text-center">
                    Erreur lors du chargement des taches {error.message}
                </div>
            )}
            empty={() => (
                <div className="text-center">Aucune chapitre disponible</div>
            )}
            inactive={() => (
                <div className="text-center">
                    Veuillez sélectionner un niveau et une matière
                </div>
            )}
        />
    )
}

export default ChapterList
