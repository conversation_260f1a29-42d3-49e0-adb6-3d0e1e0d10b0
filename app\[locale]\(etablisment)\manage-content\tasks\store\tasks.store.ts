import { create } from 'zustand'
import { createSelectors } from '@/lib/stores/selectors.store'
import { Chapter } from '@/prisma/generated/zod/modelSchema/ChapterSchema'
import { Part } from '@/prisma/generated/zod/modelSchema/PartSchema'

// Types pour les données optimisées
type ChapterWithData = {
    id: string
    title: string
    parts: PartWithData[]
}

type PartWithData = {
    id: string
    name: string
    tasks: TaskWithCompetencies[]
}

type TaskWithCompetencies = {
    id: number
    description: string
    isDisabled: boolean
    competencies: { description: string | null }[]
}

type Tasks = TasksState & TasksActions

type TasksState = {
    selectedChapter: ChapterWithData | undefined
    chaptersWithData: ChapterWithData[]
    selectedPart: PartWithData | undefined
    searchFilter: string
}

type TasksActions = {
    actions: {
        setChaptersWithData: (val: ChapterWithData[]) => void
        setSelectedChapter: (val: ChapterWithData) => void
        setSelectedPart: (val: PartWithData) => void
        setSearchFilter: (val: string) => void
    }
}

const initialTasks: TasksState = {
    selectedChapter: undefined,
    chaptersWithData: [],
    selectedPart: undefined,
    searchFilter: ''
}

const useTasks = create<Tasks>()((set, get) => ({
    ...initialTasks,
    actions: {
        setChaptersWithData: chaptersWithData =>
            set(state => ({ chaptersWithData })),
        setSelectedChapter: selectedChapter =>
            set(state => ({ selectedChapter })),
        setSelectedPart: selectedPart => set({ selectedPart }),
        setSearchFilter: searchFilter => set({ searchFilter })
    }
}))

// Ajouter un sélecteur pour récupérer les parties du chapitre sélectionné
export const getPartsFromSelectedChapter = (state: Tasks): PartWithData[] => {
    const selectedChapter = state.selectedChapter
    if (!selectedChapter) return []

    const chapterWithData = state.chaptersWithData.find(
        ch => ch.id === selectedChapter.id
    )
    return chapterWithData ? chapterWithData.parts : []
}

// Ajouter un sélecteur pour récupérer les tâches de la partie sélectionnée
export const getTasksFromSelectedPart = (
    state: Tasks
): TaskWithCompetencies[] => {
    const selectedPart = state.selectedPart
    if (!selectedPart) return []

    const parts = getPartsFromSelectedChapter(state)
    const partWithData = parts.find(p => p.id === selectedPart.id)
    return partWithData ? partWithData.tasks : []
}

export default useTasks

export const selectTasks = createSelectors(useTasks)
