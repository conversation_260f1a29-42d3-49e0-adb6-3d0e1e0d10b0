import { Link } from '@/i18n/routing'
import { But<PERSON> } from '@/components/ui/button'
import { useTranslations } from 'next-intl'
import React from 'react'

export default function InvalidVerifKey() {
    const t = useTranslations('app.auth.signup.invalid')
    return (
        <div className="w-full flex flex-col items-center">
            <h2 className="text-dinoBotBlue text-5xl font-bold my-10">
                {t('title')}
            </h2>
            <p className="mb-10">{t('text')}</p>

            <Button
                asChild
                className="bg-transparent border border-gray-500 rounded-2xl text-gray-500 hover:bg-gray-500 hover:text-white ml-2 transition-all duration-300 "
            >
                <Link href="/">{t('return')}</Link>
            </Button>
        </div>
    )
}
