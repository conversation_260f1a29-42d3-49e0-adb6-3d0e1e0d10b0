import React from 'react'

import {
    getChaptersByDomainIdLevelId,
    getPartsByChapterId
} from '../../../../../../lib/training-mode/actions'
import { selectUseContentStore } from '../../store/content.store'
import { useQueries } from '@tanstack/react-query'
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import { SelectItem } from '@radix-ui/react-select'
import { Button } from '@/components/ui/button'

export type formParamsType = {
    selectedChapter: string
    selectedPart: string
    nbQuestions: number
}

export const initFormParams: formParamsType = {
    selectedChapter: '',
    selectedPart: '',
    nbQuestions: 1
}

type FormParamsPromptPreviewProps = {
    type?: string
    setPreview: React.Dispatch<
        React.SetStateAction<
            | { question: string; knowledge: string; solution: string }[]
            | undefined
        >
    >
    dispatch: (value: Partial<formParamsType>) => void
    formState: formParamsType
    startTransition: React.TransitionStartFunction
}

function FormParamsPromptPreview({
    type,
    setPreview,
    formState: { selectedChapter, selectedPart, nbQuestions },
    dispatch,
    startTransition
}: FormParamsPromptPreviewProps) {
    const domain = selectUseContentStore.use.domain()
    const level = selectUseContentStore.use.level()
    const dinoUrl = selectUseContentStore.use.dinoUrl()

    const [{ data: chapters }, { data: parts }] = useQueries({
        queries: [
            {
                queryKey: ['chapters', domain?.id, level?.id],
                queryFn: () =>
                    getChaptersByDomainIdLevelId(domain?.id, level?.id)
            },
            {
                queryKey: ['part', selectedChapter],
                queryFn: () => getPartsByChapterId(selectedChapter)
            }
        ]
    })

    function handleGeneration() {
        const body = {
            domain: domain,
            level: level,
            chapter: selectedChapter,
            part: selectedPart,
            type,
            questionNbr: nbQuestions
        }
        startTransition(async () => {
            const response = await fetch(
                `${dinoUrl}/fr/api/generate-questions-preview`,
                {
                    cache: 'no-store',
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(body)
                }
            )

            const res = await response.json()
            setPreview(res?.questions)
        })
    }

    return (
        <div className="flex flex-row gap-2 py-2 mb-2 border-b-dinoBotLightGray border-b">
            <Select
                value={selectedChapter}
                onOpenChange={() => console.log('open')}
                onValueChange={value => {
                    dispatch({ selectedChapter: value })
                }}
            >
                <SelectTrigger className="flex-1 max-w-56">
                    <SelectValue placeholder="Sélectionnez un Chapitre" />
                </SelectTrigger>
                <SelectContent>
                    <SelectGroup>
                        {chapters?.map((chapter, i) => (
                            <SelectItem key={i} value={chapter?.id!}>
                                {chapter?.title}
                            </SelectItem>
                        ))}
                    </SelectGroup>
                </SelectContent>
            </Select>

            <Select
                value={selectedPart}
                disabled={!selectedChapter}
                onValueChange={value => {
                    dispatch({ selectedPart: value })
                }}
            >
                <SelectTrigger className="flex-1 max-w-56">
                    <SelectValue placeholder="Sélectionnez une partie" />
                </SelectTrigger>
                <SelectContent>
                    <SelectGroup>
                        {parts?.map((part, i) => (
                            <SelectItem key={i} value={part?.id!}>
                                {part?.name}
                            </SelectItem>
                        ))}
                    </SelectGroup>
                </SelectContent>
            </Select>

            <Select
                value={nbQuestions == 0 ? '' : nbQuestions.toString()}
                onValueChange={value => {
                    dispatch({ nbQuestions: parseInt(value) })
                }}
            >
                <SelectTrigger
                    className="flex-1 max-w-56"
                    disabled={!selectedPart}
                >
                    <SelectValue placeholder="questions" />
                </SelectTrigger>
                <SelectContent>
                    <SelectGroup>
                        {Array.from({ length: 10 }).map((_, i) => (
                            <SelectItem key={i} value={(i + 1).toString()}>
                                {i + 1} {i + 1 > 1 ? 'questions' : 'question'}
                            </SelectItem>
                        ))}
                    </SelectGroup>
                </SelectContent>
            </Select>
            <Button
                className="bg-[#FF7A00] flex-1 hover:bg-[#FF7A00]"
                onClick={handleGeneration}
                disabled={!selectedPart}
            >
                Generer
            </Button>
        </div>
    )
}

export default FormParamsPromptPreview
