import { NextRequest, NextResponse } from 'next/server'
import path from 'path'
import fs from 'fs'

export async function GET(req: NextRequest) {
    const urlPath = req.nextUrl.pathname
    const pathSegments = urlPath.split('/').filter(Boolean)

    if (
        pathSegments.length !== 3 ||
        pathSegments[0] !== 'api' ||
        pathSegments[1] !== 'getUploadedImage'
    ) {
        return NextResponse.json({ error: 'Invalid URL path' }, { status: 400 })
    }

    const fileName = pathSegments[2]

    try {
        const uploadDir = path.join(process.cwd(), 'public', 'uploads')
        const filePath = path.join(uploadDir, fileName)

        //console.log('Upload Directory:', uploadDir);
        //console.log('File Path:', filePath);

        if (!fs.existsSync(filePath)) {
            return NextResponse.json(
                { error: 'Image not found' },
                { status: 404 }
            )
        }

        const fileBuffer = fs.readFileSync(filePath)
        //const extension = path.extname(fileName).slice(1);
        const contentType = `image/png`

        return new NextResponse(fileBuffer, {
            headers: {
                'Content-Type': contentType,
                'Cache-Control':
                    'public, immutable, no-transform, s-maxage=31536000, max-age=31536000'
            }
        })
    } catch {
        //console.error('Error retrieving uploaded image:', error);
        return NextResponse.json(
            { error: 'Failed to retrieve image' },
            { status: 500 }
        )
    }
}
