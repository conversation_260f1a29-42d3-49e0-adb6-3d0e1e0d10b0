'use client'
import {
    Accordion,
    AccordionItem,
    AccordionTrigger
} from '@/components/ui/accordion'
import { ScrollArea } from '@/components/ui/scroll-area'
import React from 'react'
import DomainLevelButton from './domain-level-button'
import { useContentStore } from '../store/content.store'
import { useLocale } from 'next-intl'
import { getLangProps } from '@/lib/utils/string.utils'

const DomainLevelSelect = () => {
    const locale = useLocale()
    const { domains, levels, setLevelId, setLevel, setDomain } =
        useContentStore()
    return (
        <ScrollArea className="w-full">
            <Accordion type="single" collapsible className="text-dinoBotWhite ">
                {levels.map(level => (
                    <AccordionItem
                        key={level.id}
                        value={level.name}
                        className="border-b-0"
                    >
                        <AccordionTrigger
                            className="flex-row-reverse justify-end gap-2 pl-2 data-[state=open]:font-extrabold"
                            color="white"
                            onClick={() => {
                                setLevelId(level.id)
                                setLevel(level)
                            }}
                        >
                            {getLangProps({
                                obj: level,
                                base: 'name',
                                lang: locale
                            })}
                        </AccordionTrigger>
                        {domains.map(domain => (
                            <DomainLevelButton
                                key={domain.id}
                                domain={domain}
                                level={level}
                            />
                        ))}
                    </AccordionItem>
                ))}
            </Accordion>
        </ScrollArea>
    )
}

export default DomainLevelSelect
