import Link from 'next/link'
import React from 'react'
import { ClassWithPartialRelations } from '@/prisma/generated/zod/modelSchema/ClassSchema'

function CourseCard(course: ClassWithPartialRelations) {
    return (
        <Link
            className="bg-dinoBotBlue h-fit w-[300px] p-2 m-2 rounded-sm font-medium text-dinoBotWhite text-xl flex gap-1 items-start flex-col hover:cursor-pointer class-color hover:opacity-80"
            href={`my-courses/${course.id}`}
            style={{ backgroundColor: course.classColor! }}
        >
            <div className="flex gap-0.5 items-center text-xs">
                <p>{course.level?.name}</p>
                {course.domain ? (
                    <>
                        <p>&#8226;</p>
                        <p>{course.domain?.name}</p>
                    </>
                ) : null}
            </div>
            <h2 className="text-lg">{course.name}</h2>
            <h3 className="text-xs">
                {course.mainTeacher?.lastName} {course.mainTeacher?.firstName}
            </h3>
        </Link>
    )
}

export default CourseCard
