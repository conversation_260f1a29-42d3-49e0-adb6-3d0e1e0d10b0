'use server'

import { AuthResult } from '@/lib/types'
import prisma from '@/lib/prisma-client'
import { createFreeTrialForNewUser } from '@/lib/stripe/server'
import { Level } from '@/prisma/generated/zod/modelSchema/LevelSchema'
import { getLocale, getTranslations } from 'next-intl/server'
import { z } from 'zod'

export async function checkIfKeyIsValid(verifyKey: string) {
    const user = await prisma.user.findFirst({
        where: {
            verifyKey: verifyKey
        }
    })

    if (user) {
        return { type: 'success', message: 'Key is valid!', user }
    } else {
        return { type: 'error', message: 'Invalid verification key!' }
    }
}

export async function verifyEmail(
    _prevState_: AuthResult | undefined,
    _formData_: FormData
) {
    const verifyKey = _formData_.get('verificationKey') as string
    const firstName = _formData_.get('firstName') as string
    const lastName = _formData_.get('lastName') as string
    const gender = _formData_.get('gender') as string
    const level = JSON.parse(_formData_.get('level') as string) as Level
    const bday = _formData_.get('birthDate') as string
    const birthDate = new Date(bday)
    const user = await prisma.user.findFirst({
        where: {
            verifyKey: verifyKey
        }
    })

    if (user) {
        let dataToUpdate: any = {
            firstName: firstName,
            lastName: lastName,
            gender: gender,
            emailVerified: true,
            verifyKey: null,
            verifyKeyDate: null
        }

        if (user.type === 'student') {
            const twelveYearsAgo = new Date()
            twelveYearsAgo.setFullYear(twelveYearsAgo.getFullYear() - 12)

            const locale = await getLocale()
            const t = await getTranslations({ locale, namespace: 'app.auth' })

            const studentSchema = z.object({
                birthDate: z.coerce
                    .date()
                    .max(new Date(), {
                        message: t('zod.date.max')
                    })
                    .refine(date => date <= twelveYearsAgo, {
                        message: t('zod.custom.minAge')
                    })
            })

            const validationResult = studentSchema.safeParse({ birthDate })
            if (!validationResult.success) {
                const firstError = validationResult.error.errors[0].message
                return {
                    type: 'error',
                    message:
                        firstError || `Entrées invalides, veuillez réessayer !`
                }
            }

            dataToUpdate = {
                ...dataToUpdate,
                level: level.name,
                levelId: level.id,
                birthDate: validationResult.data.birthDate
            }
        }
        await prisma.user.update({
            where: {
                id: user.id
            },
            data: dataToUpdate
        })
        // Create a free trial subscription
        await createFreeTrialForNewUser(user.email)

        return { type: 'success', message: 'Email vérifié avec succès !' }
    } else {
        return {
            type: 'error',
            message: 'Erreur lors de la vérification de ton email!'
        }
    }
}
