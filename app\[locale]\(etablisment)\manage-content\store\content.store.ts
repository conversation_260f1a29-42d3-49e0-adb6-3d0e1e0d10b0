import { LevelType } from '@/prisma/generated/zod/modelSchema/LevelTypeSchema'
import { createSelectors } from '@/lib/stores/selectors.store'
import { create } from 'zustand'
import EvaluationPromptSchema from '../../../../../prisma/generated/zod/modelSchema/EvaluationPromptSchema'
import { z } from 'zod'
import { Level } from '@/prisma/generated/zod/modelSchema/LevelSchema'
import { Domain } from '@/prisma/generated/zod/modelSchema/DomainSchema'

type ContentStore = ContentState & ContentActions
type ContentState = {
    levels: Level[]
    domains: Domain[]
    schoolCycles: LevelType[]
    domainId: number | undefined
    levelId: number
    schoolCycleId: number
    levelDomainId: string
    evalPrompts: z.infer<typeof EvaluationPromptSchema>[]
    level: Level
    domain: Domain
    dinoUrl: string
}
type ContentActions = {
    setLevels: (levels: Level[]) => void
    setDomains: (domains: Domain[]) => void
    setSchoolCycles: (schoolCycles: LevelType[]) => void
    setDomainId: (id: number) => void
    setLevelId: (id: number) => void
    setSchoolCycleId: (id: number) => void
    setLevelDomainId: (id: string) => void
    setEvalPrompts: (
        evalPrompts: z.infer<typeof EvaluationPromptSchema>[]
    ) => void
    setLevel: (level: Level) => void
    setDomain: (domain: Domain) => void
    setDinoUrl: (url: string) => void
}
const initialContent: ContentState = {
    levels: [],
    domains: [],
    schoolCycles: [],
    domainId: undefined,
    levelId: 0,
    schoolCycleId: 0,
    levelDomainId: '',
    evalPrompts: [],
    level: {} as Level,
    domain: {} as Domain,
    dinoUrl: ''
}

export const useContentStore = create<ContentStore>((set, get) => ({
    ...initialContent,
    setLevels: levels => set({ levels }),
    setDomains: domains => set({ domains }),
    setSchoolCycles: schoolCycles => set({ schoolCycles }),
    setDomainId: id => set({ domainId: id }),
    setLevelId: id => set({ levelId: id }),
    setSchoolCycleId: id => set({ schoolCycleId: id }),
    setLevelDomainId: id => set({ levelDomainId: id }),
    setEvalPrompts: evalPrompts => set({ evalPrompts }),
    setLevel: level => set({ level }),
    setDomain: domain => set({ domain }),
    setDinoUrl: url => set({ dinoUrl: url })
}))
export const selectUseContentStore = createSelectors(useContentStore)
