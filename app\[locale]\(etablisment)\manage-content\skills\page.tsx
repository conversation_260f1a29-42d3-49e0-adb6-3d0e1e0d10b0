'use client'
import React from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import SkillChapterList from './components/skill-chapter-list'
import SkillFilter from './components/skill-filter'
import { useTranslations } from 'next-intl'

const SkillsPage = () => {
    const t = useTranslations('establishment.manage_content.skills')

    return (
        <div className="h-full">
            <div className="flex w-full justify-between items-center p-3">
                <h2 className=" font-bold text-xl text-dinoBotDarkGray">
                    {t('title')}
                </h2>
                <SkillFilter />
            </div>
            <div className="p-3 h-[calc(100%-60px)]">
                <ScrollArea className="h-full">
                    <div className="">
                        <SkillChapterList />
                    </div>
                </ScrollArea>
            </div>
        </div>
    )
}

export default SkillsPage
