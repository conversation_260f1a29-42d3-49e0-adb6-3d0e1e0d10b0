'use server'

import { getStringFromBuffer } from '@/lib/utils/utils'
import { z } from 'zod'
import { AuthResult } from '@/lib/types'

import { randomBytes } from 'crypto'
import prisma from '@/lib/prisma-client'
import { sendVerificationEmail } from '@/lib/mail-utils'
import { UserType } from '@prisma/client'
import { logger } from '@/logger/logger'

// Function to generate an email verification token
const generateEmailVerificationToken = () => {
    return randomBytes(32).toString('hex')
}

export async function signup(
    _prevState_: AuthResult | undefined,
    _formData_: FormData
) {
    const email = _formData_.get('email') as string
    const password = _formData_.get('password') as string
    const password2 = _formData_.get('password2') as string
    const type = _formData_.get('type') as UserType

    logger.debug(`Signing up user with email: ${email}`)

    if (password !== password2) {
        return {
            type: 'error',
            message: ` La confirmation du mot de passe est différente du mot de passe saisi !`
        }
    }

    const parsedCredentials = z
        .object({
            email: z.string().email(),
            password: z.string().min(6)
        })
        .safeParse({
            email,
            password
        })

    if (parsedCredentials.success) {
        const salt = crypto.randomUUID()
        const encoder = new TextEncoder()
        const saltedPassword = encoder.encode(password + salt)
        const hashedPasswordBuffer = await crypto.subtle.digest(
            'SHA-256',
            saltedPassword
        )
        const hashedPassword = getStringFromBuffer(hashedPasswordBuffer)

        const verifyKey = generateEmailVerificationToken()

        try {
            await prisma.user.create({
                data: {
                    email,
                    password: hashedPassword,
                    salt,
                    emailVerified: false,
                    verifyKey,
                    verifyKeyDate: new Date(),
                    type: type ? type : UserType.student
                }
            })

            sendVerificationEmail(email, verifyKey)
            /* 
      await signIn('credentials', {
        email,
        password,
        redirect: false
      }) */

            return {
                type: 'success',
                message: 'Compte créé avec succès !',
                email: email
            }
        } catch (error) {
            console.log(error)
            /*       if (error.code === 'P2002') {
        return { type: 'error', message: 'User already exists! Please log in.' }
      } else { */
            return {
                type: 'error',
                message: `Une erreur s'est produite, veuillez réessayer !`
            }
            //}
        }
    } else {
        return {
            type: 'error',
            message: `Entrées invalides, veuillez réessayer !`
        }
    }
}

export async function resendVerificationEmail(email: string) {
    logger.debug(`Resending verification email`)

    const user = await prisma.user.findFirst({
        where: {
            email: email
        }
    })

    if (user) {
        const verifyKey = generateEmailVerificationToken()
        await prisma.user.update({
            where: {
                id: user.id
            },
            data: {
                verifyKey,
                verifyKeyDate: new Date()
            }
        })

        sendVerificationEmail(email, verifyKey)

        return { type: 'success', message: 'Email sent successfully!' }
    } else {
        return { type: 'error', message: 'User not found!' }
    }
}
