'use client'
import React, { useMemo } from 'react'
import { selectSkillsStore, PartWithSkillsData } from '../store/skills.store'
import { useTranslations } from 'next-intl'

interface SkillListItemsProps {
    part: PartWithSkillsData
}

const SkillListItems = ({ part }: SkillListItemsProps) => {
    const t = useTranslations('establishment.manage_content.skills')
    const searchFilter = selectSkillsStore.use.searchFilter()

    const filteredSkills = useMemo(() => {
        if (!searchFilter.trim()) return part.skills

        return part.skills?.filter(skill =>
            skill.description
                ?.toLowerCase()
                .includes(searchFilter.toLowerCase())
        )
    }, [part.skills, searchFilter])

    if (!part) {
        return <div className="text-center">{t('select_chapter')}</div>
    }

    if (!part.skills || part.skills.length === 0) {
        return <div className="text-center">{t('no_skills_available')}</div>
    }

    return (
        <div className="flex flex-col gap-3">
            {filteredSkills?.length! > 0 ? (
                filteredSkills?.map(skill => (
                    <SkillItem key={skill.id} skill={skill} />
                ))
            ) : (
                <div className="text-center text-gray-500">
                    {searchFilter
                        ? t('no_skills_found')
                        : t('no_skills_available')}
                </div>
            )}
        </div>
    )
}

export default SkillListItems

const SkillItem = ({
    skill
}: {
    skill: { id: string; description: string | null }
}) => {
    return (
        <div className="px-2 pl-4 py-1 rounded-md bg-dinoBotLightGray/45 flex items-center justify-between text-xs">
            <h3 className="">{skill.description || 'Sans description'}</h3>
        </div>
    )
}
