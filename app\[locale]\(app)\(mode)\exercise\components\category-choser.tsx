'use client'
import useExoModeStore from '@/lib/stores/exercise-mode-store/exercise-store'
import React from 'react'
import { motion } from 'framer-motion'
import {
    IconBookOutline,
    IconEMC,
    IconGeo,
    IconHistory,
    IconPI,
    IconPhysic,
    IconSVT
} from '@/components/ui/icons'
import { ChevronLeft } from 'lucide-react'
import useTopicStore from '@/lib/stores/topic-store/topic-store'
import { useCookies } from 'next-client-cookies'
import { useLocale, useTranslations } from 'next-intl'
import { getLangDir } from 'rtl-detect'
import { Domain } from '@prisma/client'

function CategoryChoser() {
    const setSubject = useExoModeStore(state => state.setSubject)
    const setTopic = useTopicStore(state => state.setTopic)
    const setLevel = useExoModeStore(state => state.setLevel)
    const cookies = useCookies()
    const t = useTranslations('app.mode.exo.module')

    const locale = useLocale()
    const dir = getLangDir(locale)
    const reset = () => {
        setSubject(null)
        setLevel(null)
    }

    const handleSubjectChange = (subject: string) => {
        setSubject(subject)
        setTopic(JSON.parse(subject) as Domain)
        cookies.set('topic', subject)
    }

    return (
        <motion.div
            initial={{ x: 1000, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -1000, opacity: 0 }}
            transition={{ duration: 0.5 }}
            className="relative flex flex-col justify-center items-center gap-8 sm:gap-11 bg-transparent"
        >
            <button
                onClick={reset}
                className={`flex justify-center items-center top-0  sm:top-2 -left-10 sm:-left-16 absolute size-8 bg-dinoBotBlue hover:bg-dinoBotBlue/80 text-white rounded-full ${dir === 'rtl' ? 'scale-x-[-1]' : ''} `}
            >
                <ChevronLeft className="size-6 mr-1" />
            </button>
            <h2 className="text-center text-2xl sm:text-5xl font-extrabold underline text-dinoBotVibrantBlue">
                {t('chois')}
            </h2>
            {/* <div className="w-60 sm:size-auto grid grid-cols-2 grid-rows-4 sm:grid-cols-4 sm:grid-rows-2 gap-5 "> */}
            <div className="w-60 sm:size-auto flex flex-col gap-5 ">
                <div className="flex gap-5 flex-col sm:flex-row sm:justify-center">
                    <div className="flex gap-5 justify-center">
                        <motion.div
                            whileHover={{
                                scale: 1.1,
                                transition: {
                                    type: 'spring',
                                    stiffness: 1000,
                                    damping: 10
                                }
                            }}
                            onClick={() => handleSubjectChange('Mathématiques')}
                            className="relative size-28 bg-dinoBotRed flex flex-col justify-center items-center  rounded-xl   cursor-pointer "
                        >
                            <IconPI className="size-12 mb-3" />
                            <div className="text-xs font-extrabold text-white absolute bottom-3">
                                {t('math')}
                            </div>
                        </motion.div>
                    </div>
                    <div className="flex gap-5">
                        <motion.div
                            whileHover={{
                                scale: 1.1,
                                transition: {
                                    type: 'spring',
                                    stiffness: 1000,
                                    damping: 10
                                }
                            }}
                            onClick={() => handleSubjectChange('Français')}
                            className="relative size-28 bg-dinoBotSky flex flex-col justify-center items-center  rounded-xl  cursor-pointer "
                        >
                            <IconBookOutline className="size-12 mb-3 fill-none stroke-white" />
                            <div className="text-xs font-extrabold text-white absolute bottom-3">
                                {t('fr')}
                            </div>
                        </motion.div>
                        <motion.div
                            whileHover={{
                                scale: 1.1,
                                transition: {
                                    type: 'spring',
                                    stiffness: 1000,
                                    damping: 10
                                }
                            }}
                            onClick={() =>
                                handleSubjectChange('Physique-Chimie')
                            }
                            className="relative size-28 bg-dinoBotCyan flex flex-col justify-center items-center  rounded-xl   cursor-pointer "
                        >
                            <IconPhysic className="size-14 mb-3 stroke-none fill-white" />
                            <div className="text-xs font-extrabold text-white absolute bottom-3 text-nowrap">
                                {t('physique-chimie')}
                            </div>
                        </motion.div>
                    </div>
                </div>
                <div className="flex gap-5 flex-col sm:flex-row sm:justify-center">
                    <div className="flex gap-5">
                        <motion.div
                            // whileHover={{
                            //   scale: 1.1,
                            //   transition: {
                            //     type: 'spring',
                            //     stiffness: 1000,
                            //     damping: 10
                            //   }
                            // }}
                            // onClick={() => handleSubjectChange('SVT')}
                            className="relative size-28 bg-dinoBotLightGray flex flex-col justify-center items-center  rounded-xl  cursor-not-allowed "
                            // className="relative size-28 bg-dinoBotGreen flex flex-col justify-center items-center  rounded-xl  cursor-pointer "
                        >
                            <IconSVT className="size-12 mb-3 fill-none stroke-white" />
                            <div className="text-xs font-extrabold text-white absolute bottom-3">
                                {t('svt')}
                            </div>
                        </motion.div>
                        <motion.div
                            // whileHover={{
                            //   scale: 1.1,
                            //   transition: {
                            //     type: 'spring',
                            //     stiffness: 1000,
                            //     damping: 10
                            //   }
                            // }}
                            // onClick={() => handleSubjectChange('EMC')}
                            className="relative size-28 bg-dinoBotLightGray flex flex-col justify-center items-center  rounded-xl  cursor-not-allowed "
                            // className="relative size-28 bg-dinoBotRoseBonbon flex flex-col justify-center items-center  rounded-xl  cursor-pointer "
                        >
                            <IconEMC className="size-12 mb-3 fill-none stroke-white" />
                            <div className="text-xs font-extrabold text-white absolute bottom-3">
                                {t('emc')}
                            </div>
                        </motion.div>
                    </div>
                    <div className="flex gap-5 justify-center">
                        <motion.div
                            // whileHover={{
                            //   scale: 1.1,
                            //   transition: {
                            //     type: 'spring',
                            //     stiffness: 1000,
                            //     damping: 10
                            //   }
                            // }}
                            // onClick={() => handleSubjectChange('Géographie')}
                            className="relative size-28 bg-dinoBotLightGray flex flex-col justify-center items-center  rounded-xl  cursor-not-allowed "
                            // className="relative size-28 bg-dinoBotPurple flex flex-col justify-center items-center  rounded-xl cursor-pointer "
                        >
                            <IconGeo className="size-12 mb-3 stroke-white" />
                            <div className="text-xs font-extrabold text-white absolute bottom-3">
                                {t('geo')}
                            </div>
                        </motion.div>
                        <motion.div
                            // whileHover={{
                            //   scale: 1.1,
                            //   transition: {
                            //     type: 'spring',
                            //     stiffness: 1000,
                            //     damping: 10
                            //   }
                            // }}
                            // onClick={() => handleSubjectChange('Histoire')}
                            className="relative size-28 bg-dinoBotLightGray flex flex-col justify-center items-center  rounded-xl  cursor-not-allowed "
                            // className="relative size-28 bg-dinoBotYellow flex flex-col justify-center items-center  rounded-xl  cursor-pointer "
                        >
                            <IconHistory className="size-12 mb-3 fill-none stroke-white" />
                            <div className="text-xs font-extrabold text-white absolute bottom-3">
                                {t('history')}
                            </div>
                        </motion.div>
                    </div>
                </div>
            </div>
        </motion.div>
    )
}

export default CategoryChoser
