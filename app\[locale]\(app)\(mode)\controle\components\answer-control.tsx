'use client'
import { selectUseCtrlModeStore } from '@/app/[locale]/(app)/(mode)/controle/store/controle-store'
import { cn } from '@/lib/utils/utils'
import { Radical, Save } from 'lucide-react'
import Loading from './loading'
import { toast } from 'sonner'
import { ControlFeedbackOutput } from '@/lib/control-mode/types/control/types'
import useCortexStore from '@/app/[locale]/(app)/(mode)/controle/store/control-cortex-store'
import QuillTextArea from './control-text-area'
import ControlCortexDialog from './control-cortex-dialog'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import PaginationExos from './pagination-exos'
import { useLocale, useTranslations } from 'next-intl'
import { getLangDir } from 'rtl-detect'
import { selectUseAvatarStore } from '@/lib/stores/avatar-store/avatar-store'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeRaw from 'rehype-raw'
import rehypemathjax from 'rehype-mathjax'
import { MemoizedReactMarkdown } from '@/components/markdown'
import {
    Tooltip,
    TooltipContent,
    TooltipTrigger
} from '@/components/ui/tooltip'
import { Button } from '@/components/ui/button'
import { IconEdit } from '@/components/ui/icons'

interface AnswerControleProps {
    generateControlFeedback: (
        data: ControlFeedbackOutput
    ) => Promise<ControlFeedbackOutput | null>
}

const AnswerControl = ({ generateControlFeedback }: AnswerControleProps) => {
    const t = useTranslations('app.mode.controle.answerControl')
    const setKnowledgeBaseModeContent =
        selectUseAvatarStore.use.setKnowledgeBaseModeContent()
    const locale = useLocale()
    const dir = getLangDir(locale)
    const { openCortexDialog, text, setid, initValue, setText } =
        useCortexStore()
    const controle = selectUseCtrlModeStore.use.controle()
    const setControle = selectUseCtrlModeStore.use.setControle()
    const isRunning = selectUseCtrlModeStore.use.isRunning()
    const setIsRunning = selectUseCtrlModeStore.use.setIsRunnig()
    const isLoading = selectUseCtrlModeStore.use.isLoading()
    const setLoading = selectUseCtrlModeStore.use.setLoding()
    const pause = selectUseCtrlModeStore.use.pauseTime()
    const [hiddenSubmit, setHiddenSubmit] = useState(false)
    const [indexAnswer, setIndexAnswer] = useState(0)
    const flag = useRef(true)
    const isTimerFinich = useRef(true)

    const [localAnswers, setLocalAnswers] = useState<string[][]>([])

    useEffect(() => {
        if (flag.current) {
            return
        }
        const initialAnswers = Array.from(
            { length: controle?.exercises.length || 0 },
            () =>
                Array.from(
                    { length: controle?.exercises[0]?.questions.length || 0 },
                    () => ''
                )
        )
        initValue(initialAnswers)
        setLocalAnswers(initialAnswers)
        flag.current = false
    }, [])

    useEffect(() => {
        if (controle)
            setKnowledgeBaseModeContent(
                'voicie le controle en json' + JSON.stringify(controle)
            )
    }, [controle])

    const handleLocalAnswerChange = (
        value: string,
        exoIndex: number,
        questionIndex: number
    ) => {
        setLocalAnswers(prev => {
            const newAnswers = [...prev]
            if (!newAnswers[exoIndex]) {
                newAnswers[exoIndex] = []
            }
            newAnswers[exoIndex][questionIndex] = value
            return newAnswers
        })
    }

    const handleCortex = (idanswer: number, idcontent: number) => {
        openCortexDialog()
        setid(idanswer, idcontent)
    }
    // const change = (
    //     e: ChangeEvent<HTMLTextAreaElement>,
    //     exo: number,
    //     question: number
    // ) => {
    //     setValueInput({ exo, question, answer: e.target.value })
    // }

    const handleUpdate = (exo: number, question: number) => {
        const newControle = controle?.exercises || []
        const questionContent = newControle[exo].questions[question]
        const saved = questionContent.saved
        newControle[exo].questions[question] = {
            ...questionContent,
            answer: text[exo][question],
            saved: !saved
        }
        setControle({ ...controle, exercises: newControle })
    }

    const isAllQuestionsSaved = () => {
        if (!controle) return false
        return controle.exercises.every(exercise =>
            exercise.questions.every(question => question.saved)
        )
    }

    const handleSubmit = useCallback(async () => {
        try {
            if (!controle) throw new Error(t('errorMessage'))
            else if (!isAllQuestionsSaved())
                throw new Error(t('saveAllResponsePlease'))
            setLoading(true)
            pause()
            setHiddenSubmit(true)
            const result = await generateControlFeedback(controle)
            if (result) {
                setLoading(false)
                setIsRunning(false)
                toast.success(t('successMessage'))
                setControle(result)
            } else {
                toast.error(t('errorMessage'))
            }
        } catch (error) {
            const err = error as Error
            toast.error(err.message)
        }
    }, [controle, generateControlFeedback, pause, setControle, setLoading, t])

    // verify fi the timer need to be reset
    const shouldResetTimer = () =>
        !isLoading && isRunning && isTimerFinich.current

    // Reset timer state
    const resetTimer = () => {
        isTimerFinich.current = false
    }

    const saveAllQuestions = () => {
        if (controle) {
            controle.exercises.forEach((exercise, exoIndex) => {
                exercise.questions.forEach((question, questionIndex) => {
                    if (!question.saved) {
                        handleUpdate(exoIndex, questionIndex)
                    }
                })
            })
        }
    }

    // Verify if the conditions for submission are met
    const shouldSubmit = () =>
        !isRunning && !isTimerFinich.current && !isLoading

    useEffect(() => {
        if (shouldResetTimer()) {
            resetTimer()
            return
        }

        if (shouldSubmit()) {
            saveAllQuestions()
            handleSubmit()
        }
    }, [isRunning])

    return (
        <div className=" flex flex-col size-full items-center">
            <div
                className={`size-4/5 mx-5 my-3 2xl:my-8 border-gray-300 border-2 rounded p-8 xl:overflow-y-auto relative ${hiddenSubmit ? 'hidden' : ''}`}
            >
                {isLoading ? (
                    <Loading />
                ) : (
                    <>
                        <div className="w-full flex justify-center">
                            <PaginationExos
                                index={indexAnswer}
                                setIndex={setIndexAnswer}
                            />
                        </div>
                        <div className="flex flex-col items-center">
                            <div className="flex flex-col w-full">
                                {controle?.exercises[indexAnswer].questions.map(
                                    (content, questionIndex) => (
                                        <div
                                            key={'div ' + content.id}
                                            className="flex flex-col w-full"
                                        >
                                            <MemoizedReactMarkdown
                                                remarkPlugins={[
                                                    remarkGfm,
                                                    remarkMath
                                                ]}
                                                rehypePlugins={[
                                                    rehypeRaw,
                                                    rehypemathjax
                                                ]}
                                                components={{
                                                    p({ children }) {
                                                        return (
                                                            <p
                                                                className={cn(
                                                                    'prose prose-red break-words dark:prose-invert prose-p:leading-relaxed prose-pre:p-0',
                                                                    'prose-p:text-sm prose-p:mt-1',
                                                                    'mb-2 last:mb-0'
                                                                )}
                                                            >
                                                                {content.id + 1}
                                                                . {children}
                                                            </p>
                                                        )
                                                    }
                                                }}
                                            >
                                                {content.questionContent}
                                            </MemoizedReactMarkdown>
                                            <div className="flex w-full gap-1 items-center">
                                                <textarea
                                                    className="hidden border-2 border-gray-300 rounded-lg w-11/12 p-2"
                                                    disabled={
                                                        !isRunning ||
                                                        content.saved
                                                    }
                                                    placeholder={t(
                                                        'placeholder'
                                                    )}
                                                    onChange={e =>
                                                        setText(
                                                            e.target.value,
                                                            indexAnswer,
                                                            questionIndex
                                                        )
                                                    }
                                                ></textarea>
                                                <QuillTextArea
                                                    key={
                                                        controle?.exercises[
                                                            indexAnswer
                                                        ].id +
                                                        '' +
                                                        content.id
                                                    }
                                                    placeholder={t(
                                                        'placeholder'
                                                    )}
                                                    disable={
                                                        !isRunning ||
                                                        content.saved!
                                                    }
                                                    answerid={indexAnswer}
                                                    contentid={questionIndex}
                                                    value={
                                                        localAnswers[
                                                            indexAnswer
                                                        ]?.[questionIndex] || ''
                                                    }
                                                    onChange={value => {
                                                        handleLocalAnswerChange(
                                                            value,
                                                            indexAnswer,
                                                            questionIndex
                                                        )
                                                        setText(
                                                            value,
                                                            indexAnswer,
                                                            questionIndex
                                                        )
                                                    }}
                                                    theme={'snow'}
                                                    className={`chat border-2 border-gray-300 rounded-lg w-11/12 p-2 ${!isRunning || content.saved ? ` cursor-not-allowed placeholder:text-dinoBotRed/50 placeholder:font-bold` : ''} resize-none bg-transparent  focus-within:outline-none sm:text-sm ${dir === 'rtl' ? 'rtl' : ''}`}
                                                />
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button
                                                            type="button"
                                                            //variant="outline"
                                                            size="icon"
                                                            disabled={
                                                                !isRunning ||
                                                                content.saved
                                                            }
                                                            className={`${!isRunning || content.saved ? 'cursor-not-allowed' : ''} z-10  size-8 rounded-full bg-background p-0 sm:right-24 text-dinoBotRed hover:text-white dark:hover:text-gray-50 hover:bg-dinoBotRed hover:border-dinoBotRed hover:border transition-all duration-500`}
                                                            onClick={() =>
                                                                handleCortex(
                                                                    indexAnswer,
                                                                    questionIndex
                                                                )
                                                            }
                                                        >
                                                            {/* <ImagePlus className="" /> */}
                                                            <Radical />
                                                            <span className="sr-only">
                                                                {t(
                                                                    'addMathFormula'
                                                                )}
                                                            </span>
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent className="bg-dinoBotRed">
                                                        {t('addMathFormula')}
                                                    </TooltipContent>
                                                </Tooltip>
                                            </div>

                                            <Button
                                                className="bg-transparent text-dinoBotVibrantBlue pl-1 shadow-none hover:bg-slate-300 border-none w-fit "
                                                onClick={() =>
                                                    handleUpdate(
                                                        indexAnswer,
                                                        questionIndex
                                                    )
                                                }
                                                disabled={
                                                    !isRunning || hiddenSubmit
                                                }
                                            >
                                                {content.saved ? (
                                                    <>
                                                        <IconEdit />
                                                        {t('editAnswer')}
                                                    </>
                                                ) : (
                                                    <>
                                                        <Save size={16} />
                                                        {t('saveAnswer')}
                                                    </>
                                                )}
                                            </Button>
                                        </div>
                                    )
                                )}
                            </div>
                        </div>
                    </>
                )}
            </div>
            <Button
                className={`w-fit px-8 rounded-xl bg-dinoBotVibrantBlue ${hiddenSubmit ? 'hidden' : ''} `}
                disabled={!isRunning}
                onClick={handleSubmit}
            >
                {' '}
                {t('submitAnswer')}
            </Button>
            <ControlCortexDialog />
        </div>
    )
}

export default AnswerControl
