import { type Metadata } from 'next'
import { notFound, redirect } from 'next/navigation'
import React from 'react'
import { auth } from '@/auth'
import { getChat, getMissingKeys } from '@/app/[locale]/actions'
// import { Chat } from '@/components/chat/chat'
import { Session } from '@/lib/types'
import Chat from '../../../components/chat'
import { getFeatureRoutesByFeatureName } from '@/lib/features/server'
import { FeatureFlagName } from '@prisma/client'
import { getLocale } from 'next-intl/server'
import { redirect as i18nRedirect } from '@/i18n/routing'

export interface ChatPageProps {
    params: Promise<{
        id: string
    }>
}

export async function generateMetadata(
    props: ChatPageProps
): Promise<Metadata> {
    const params = await props.params
    const session = await auth()

    if (!session?.user) {
        return {}
    }

    const chat = await getChat(params.id, session.user.id)
    return {
        title: chat?.title.toString().slice(0, 50) ?? 'Chat'
    }
}

const ChatPage = async (props: ChatPageProps) => {
    const params = await props.params
    const session = (await auth()) as Session
    const missingKeys = await getMissingKeys()

    const locale = await getLocale()

    const featureFlags = await getFeatureRoutesByFeatureName(
        FeatureFlagName.STUDENT_CHAT_MODE
    )

    if (Array.isArray(featureFlags) && featureFlags[0] != null) {
        if (featureFlags[0] === 'not-found') notFound()
        i18nRedirect({ href: `/${featureFlags[0]}`, locale })
    }

    if (!session?.user) {
        //console.log('No session found, redirecting to login');
        redirect(`/login?next=/chat/${params.id}`)
    }

    const userId = session.user.id as string
    // console.log('User ID:', userId);
    // console.log('Chat ID:', params.id);

    const chat = await getChat(params.id, userId)
    // console.log('Chat:', JSON.stringify(chat, null, 2));
    // console.log("session:",session)
    // console.log("missingkeys:",missingKeys)

    /*   if (!chat) {
      logger.debug('Chat not found, redirecting to home, chat id :' + params.id);
      redirect('/')
    } */

    if (chat?.userId !== session?.user?.id) {
        //console.log('Chat user ID does not match session user ID, showing not found');
        notFound()
    }

    return (
        // <AI
        //     initialAIState={{
        //         chatId: chat.id,
        //         topic: chat.topic,
        //         messages: chat.messages,
        //         saved: false,
        //         chatPath: chat.path,
        //         exerciseId: chat.exerciseId
        //     }}
        // >
        //     <Chat
        //         //id={chat.id}
        //         session={session}
        //         initialMessages={chat.messages}
        //         missingKeys={missingKeys}
        //         ttssttMode={process.env.TTS_STT_MODE === 'true' || false}
        //     />
        // </AI>
        <Chat session={session} idp={params.id} missingKeys={missingKeys} />
    )
}
export default ChatPage
