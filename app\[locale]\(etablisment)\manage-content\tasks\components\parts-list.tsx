import React from 'react'
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger
} from '@/components/ui/accordion'
import ListTasks from './list-tasks'

type ChapterWithData = {
    id: string
    title: string
    parts: PartWithData[]
}

type PartWithData = {
    id: string
    name: string
    tasks: TaskWithCompetencies[]
}

type TaskWithCompetencies = {
    id: number
    description: string
    isDisabled: boolean
    competencies: { description: string | null }[]
}

interface PartsListProps {
    chapter: ChapterWithData
}

const PartsList = ({ chapter }: PartsListProps) => {
    if (!chapter) {
        return (
            <div className="text-center">Veuillez sélectionner un chapitre</div>
        )
    }

    // Filtrer les parties qui ont au moins 1 tâche
    const partsWithTasks = chapter.parts.filter(
        part => part.tasks && part.tasks.length > 0
    )

    if (partsWithTasks.length === 0) {
        return (
            <div className="text-center">
                Aucune partie avec des tâches disponible
            </div>
        )
    }

    return (
        <Accordion type="multiple" className="flex flex-col gap-2">
            {partsWithTasks.map(part => (
                <AccordionItem
                    key={part.id}
                    value={part.id}
                    className=" rounded-md border-b-0"
                >
                    <AccordionTrigger
                        className="flex-row-reverse justify-end gap-2 pl-2 "
                        iconClassName="size-6"
                    >
                        <div className="flex items-center justify-start gap-1 text-sm">
                            <span className="font-semibold">Partie :</span>{' '}
                            {part.name}
                            <span className="text-sm text-gray-500">
                                ({part.tasks?.length || 0} tâches)
                            </span>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-2">
                        <ListTasks part={part} />
                    </AccordionContent>
                </AccordionItem>
            ))}
        </Accordion>
    )
}

export default PartsList
