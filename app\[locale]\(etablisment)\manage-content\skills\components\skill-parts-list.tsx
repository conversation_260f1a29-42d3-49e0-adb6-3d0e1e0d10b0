'use client'
import React from 'react'
import { ChapterWithSkillsData } from '../store/skills.store'
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger
} from '@/components/ui/accordion'
import SkillListItems from './skill-list-items'
import { useTranslations, useLocale } from 'next-intl'
import { getLangProps } from '@/lib/utils/string.utils'

interface SkillPartsListProps {
    chapter: ChapterWithSkillsData
}

const SkillPartsList = ({ chapter }: SkillPartsListProps) => {
    const t = useTranslations('establishment.manage_content.skills')
    const locale = useLocale()

    if (!chapter) {
        return <div className="text-center">{t('select_chapter')}</div>
    }

    // Filtrer les parties qui ont au moins 1 compétence
    const partsWithSkills = chapter.parts.filter(
        part => part.skills && part.skills.length > 0
    )

    if (partsWithSkills.length === 0) {
        return <div className="text-center">{t('no_parts_available')}</div>
    }

    return (
        <Accordion type="multiple" className="flex flex-col gap-2">
            {partsWithSkills.map(part => (
                <AccordionItem
                    key={part.id}
                    value={part.id}
                    className=" rounded-md border-b-0"
                >
                    <AccordionTrigger
                        className="flex-row-reverse justify-end gap-2 pl-2 "
                        iconClassName="size-6"
                    >
                        <div className="flex items-center justify-start gap-1 text-sm">
                            <span className="font-semibold">
                                {t('part_label')}
                            </span>{' '}
                            {getLangProps({
                                obj: part,
                                base: 'name',
                                lang: locale
                            })}
                            <span className="text-sm text-gray-500">
                                ({part.skills?.length || 0} {t('skills_count')})
                            </span>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-2">
                        <SkillListItems part={part} />
                    </AccordionContent>
                </AccordionItem>
            ))}
        </Accordion>
    )
}

export default SkillPartsList
