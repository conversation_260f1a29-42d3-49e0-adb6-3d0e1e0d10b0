import React from 'react'

interface AppLayoutProps {
    children: React.ReactNode
}

function ModeLayout({ children }: AppLayoutProps) {
    return (
        <div className="flex flex-col size-full ">
            {/* <LoadingBar /> */}
            <main className="flex flex-col flex-1 bg-muted/50 h-[calc(100vh-115px)] overflow-y-auto app-scroller ">
                {children}
            </main>
        </div>
    )
}

export default ModeLayout
