import { OctagonAlert } from 'lucide-react'
import { useTranslations } from 'next-intl'
import React from 'react'

function ImageNotFound() {
    const t = useTranslations('app.mode.controle.controlPreview')
    return (
        <div className="flex gap-1.5 items-center flex-col text-dinoBotRed justify-center text-center">
            <OctagonAlert className="size-6 mb-0.5" />
            <p>{t('imageNotFound')}</p>
        </div>
    )
}

export default ImageNotFound
