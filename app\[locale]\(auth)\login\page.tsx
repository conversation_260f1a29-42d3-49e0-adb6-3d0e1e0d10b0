import { auth } from '@/auth'
import LoginForm from '@/components/login/login-form'
import { Session } from '@/lib/types'
import { redirect } from '@/i18n/routing'
import { getLocale } from 'next-intl/server'
import React from 'react'
import { RegistrationType } from '@/types/types'

export default async function LoginPage() {
    const session = (await auth()) as Session
    const locale = await getLocale()
    const registrationType =
        process.env.REGISTRATION_TYPE != ''
            ? (process.env.REGISTRATION_TYPE as RegistrationType)
            : 'none'

    if (session) {
        redirect({ href: '/', locale })
    }

    return (
        <main className="size-full flex flex-col overflow-hidden">
            <LoginForm registrationType={registrationType} />
        </main>
    )
}
