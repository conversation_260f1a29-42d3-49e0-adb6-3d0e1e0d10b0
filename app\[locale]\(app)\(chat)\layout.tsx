import React from 'react'
import { SidebarDesktop } from '@/components/sidebar/sidebar-desktop'
import { FilesSidebarDesktop } from '@/components/sidebar/sidebar-files-desktop'
import Calculator, {
    CalculatorTrigger
} from '@/components/calculater/calculator'
import { Header } from '@/components/header/header'
import { SubHeader } from '@/components/sub-header/sub-header'
import AvatarFloat from '@/components/avatar/avatar-float'
import StudentSidebar from '../components/student-sidebar'
import { auth, getUser, signOut } from '@/auth'
import UserTypeSchema from '@/prisma/generated/zod/inputTypeSchemas/UserTypeSchema'
import EstablishmentSideBar from '../../(etablisment)/components/establishment-sidebar'
import { Session } from '@/lib/types'
import { getLocale } from 'next-intl/server'
import { redirect } from '@/i18n/routing'
import { User } from '@/prisma/generated/zod/modelSchema/UserSchema'

interface ChatLayoutProps {
    children: React.ReactNode
}

export default async function ChatLayout({ children }: ChatLayoutProps) {
    const HEYGEN_AVATAR_KEY = process.env.HEYGEN_AVATAR_KEY || ''
    const HEYGEN_VOICE_ID = process.env.HEYGEN_VOICE_ID || ''
    const LoginMode =
        (process.env.NEXT_PUBLIC_LOGIN_TYPE ?? process.env.LOGIN_TYPE) ==
        'dinobot'
            ? true
            : false
    const session = (await auth()) as Session
    const user = await getUser(session?.user?.email)
    const locale = await getLocale()

    const disconnect = async () => {
        'use server'
        await signOut()
        redirect({ href: '/', locale })
    }
    return (
        <div className="flex flex-row w-screen h-screen overflow-hidden relative">
            <div className="bg-dinoBotBlue flex flex-col z-10 w-fit">
                {user?.type === UserTypeSchema.enum.establishment ? (
                    <EstablishmentSideBar
                        user={user as User}
                        logOut={disconnect}
                    />
                ) : (
                    <StudentSidebar />
                )}
            </div>
            <div className="flex flex-col size-full overflow-hidden #overflow-y-auto grow">
                {/* <LoadingBar /> */}
                <div className="sticky max-h-28 top-0 flex flex-col grow">
                    <Header />
                    {LoginMode && <SubHeader />}
                </div>
                <main className="flex flex-col flex-1 bg-muted/50 h-[calc(100vh-115px)] overflow-y-auto app-scroller ">
                    <div className="relative flex h-[calc(100vh_-_theme(spacing.20))] overflow-hidden">
                        <AvatarFloat
                            avatarKey={HEYGEN_AVATAR_KEY}
                            voiceId={HEYGEN_VOICE_ID}
                        />
                        <SidebarDesktop />
                        {children}
                        <FilesSidebarDesktop />
                    </div>
                </main>
                <CalculatorTrigger />
                <Calculator />
            </div>
        </div>
    )
}
