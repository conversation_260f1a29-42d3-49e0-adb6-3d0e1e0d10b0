'use client'

import { IconUser } from '@/components/ui/icons'
import { Mic } from 'lucide-react'
import { cn } from '@/lib/utils/utils'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypemathjax from 'rehype-mathjax'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from 'public/dinobot-logo-chat.svg'
import Image from 'next/image'
import PDFLogo from 'public/pdf-logo.jpg'
import React from 'react'
import usePDFViewerStore from '@/lib/stores/pdf-viewer-store/pdf-viewer-store'
import { MathJax, MathJaxContext } from 'better-react-mathjax'
import { MathJax3Config } from 'better-react-mathjax'
import { MemoizedReactMarkdown } from '@/components/markdown'
import { memo } from 'react'
import rehypeRaw from 'rehype-raw'

const MathJaxConfig: MathJax3Config = {
    loader: { load: ['[tex]/html'] },
    tex: {
        packages: { '[+]': ['html'] },
        inlineMath: [
            ['\\(', '\\)'],
            ['$', '$']
        ],
        displayMath: [
            ['\\[', '\\]'],
            ['$$', '$$']
        ],
        extensions: ['noErrors.js', 'noUndefined.js']
    }
}

// Different types of message bubbles.

export function UserMessage({
    children,
    file,
    audio,
    transcribedAudio,
    audioDuration
}: {
    children: React.ReactNode
    file: any
    audio: string
    transcribedAudio: string
    audioDuration?: number | null
}) {
    const { setCurrentPDF, setIsPDFViewerOpen } = usePDFViewerStore()

    const getMimeType = () => {
        if (!file || !file.name) return 'application/octet-stream'
        if (file.name.endsWith('pdf')) return 'application/pdf'
        if (file.name.match(/\.(jpe?g|png|gif|webp|bmp)$/i)) {
            const ext = file.name
                .split('.')
                .pop()
                .toLowerCase()
                .replace('jpg', 'jpeg')
            return `image/${ext}`
        }
        return 'application/octet-stream'
    }

    const fileblob = (() => {
        try {
            if (!file || !file.file) return null
            // Vérifier si la chaîne est au format data URL
            const base64Data = file.file.includes(',')
                ? file.file.split(',')[1]
                : file.file
            return new Blob(
                [Uint8Array.from(atob(base64Data), c => c.charCodeAt(0))],
                { type: getMimeType() }
            )
        } catch (error) {
            console.error('Error creating blob:', error)
            return null
        }
    })()
    return (
        <div className="group relative flex items-start justify-end md:-ml-12 mb-8">
            <div className="relative mt-2 ml-4 w-fit text-white bg-dinoBotBlue space-y-2 p-2 px-4 rounded-2xl font-inter">
                <MathJaxContext config={MathJaxConfig}>
                    <MathJax dynamic>{children}</MathJax>
                </MathJaxContext>
                {file && !file?.name.endsWith('pdf') ? (
                    <Image
                        onClick={() => {
                            setCurrentPDF({
                                name: file?.name,
                                type: 'image',
                                data: URL.createObjectURL(fileblob!)
                            })
                            setIsPDFViewerOpen(true)
                        }}
                        src={URL.createObjectURL(fileblob!)}
                        alt={file?.name}
                        title={"Ouvrir l'image"}
                        width={384}
                        height={250}
                        className="w-96 mt-4 cursor-pointer"
                    />
                ) : null}
                {file && file?.name.endsWith('pdf') ? (
                    <div
                        onClick={() => {
                            setCurrentPDF({
                                name: file?.name,
                                type: 'pdf',
                                data: fileblob
                            })
                            setIsPDFViewerOpen(true)
                        }}
                        title={file?.name}
                        className="w-32 p-2 border bg-white border-white rounded-2xl mt-4  cursor-pointer"
                    >
                        <Image
                            src={PDFLogo}
                            alt=""
                            className="object-cover w-32 h-34 my-2 rounded-2xl"
                        />
                        <div className="truncate text-sm text-dinoBotBlue border-t border-dinoBotBlue">
                            {file?.name}
                        </div>
                    </div>
                ) : null}
                {transcribedAudio ? (
                    <div className="">
                        <span className="font-mono ml-2">
                            {transcribedAudio}
                        </span>
                    </div>
                ) : null}
            </div>
            {audio && (
                <div className="absolute -bottom-6 right-14 flex items-center gap-1 text-xs text-gray-500">
                    <Mic size={12} />
                    <span>
                        {audioDuration ? `${Math.round(audioDuration)}s` : ''}
                    </span>
                </div>
            )}
            <div className="flex size-10 shrink-0 ml-2 select-none items-center justify-center rounded-md border bg-background shadow-sm">
                <IconUser className="size-6" />
            </div>
        </div>
    )
}

export const BotMessage = memo(function BotMessage({
    content,
    className,
    textClassName
}: {
    content: string
    className?: string
    textClassName?: string
}) {
    return (
        <div
            className={cn(
                'group relative flex items-start md:-ml-12 mb-8',
                className
            )}
        >
            <Image src={DinoBotLogo} alt="DinoBot Logo" className="w-10 " />

            <MathJaxContext config={MathJaxConfig}>
                <MathJax dynamic>
                    <div className="mt-2 ml-4 w-fit space-y-2 p-2 px-4 overflow-hidden overflow-x-auto bg-dinoBotLightBlue rounded-2xl font-inter">
                        <MemoizedReactMarkdown
                            remarkPlugins={[remarkGfm, remarkMath]}
                            rehypePlugins={[rehypeRaw, rehypemathjax]}
                            components={{
                                p({ children }) {
                                    return (
                                        <p
                                            className={cn(
                                                'prose prose-red break-words dark:prose-invert prose-p:leading-relaxed prose-pre:p-0',
                                                textClassName,
                                                'mb-2 last:mb-0'
                                            )}
                                        >
                                            {children}
                                        </p>
                                    )
                                }
                            }}
                        >
                            {content}
                        </MemoizedReactMarkdown>
                    </div>
                </MathJax>
            </MathJaxContext>
        </div>
    )
})

export function BotErrorMessage({
    className,
    content
}: {
    className?: string
    content: string
}) {
    // const errorTexts = [
    //   `Oups, il semblerait que j'aie un petit problème technique. Sois rassuré, mes ingénieurs s'activent pour le résoudre au plus vite ! Pourrais-tu réessayer dans quelques instants ?`,
    //   `Oh, une petite embûche sur notre route ! Ne t'inquiètes pas, je vais tout de suite appeler mon équipe de dépannage. Ils sont très réactifs d'habitude. Reviens me voir dans un petit moment !`,
    //   `Aïe, un accroc imprévu là ! Pas de panique, je vais faire de mon mieux pour remettre tout en ordre rapidement. Pourquoi ne prendrais-tu pas une petite pause bien méritée ? Je serai ravi de t'accueillir à nouveau d'ici peu !`,
    //   `Désolé pour ce contretemps technique ! Mes programmeurs sont des as, ils ont déjà dû se mettre au travail. Que dirais-tu de faire une pause café/thé en attendant que je sois de nouveau opérationnel ?`,
    // ];

    // const text = useStreamableText("error")

    return (
        <div
            className={cn(
                'group relative flex items-start md:-ml-12',
                className
            )}
        >
            <Image src={DinoBotLogo} alt="DinoBot Logo" className="w-10 " />

            <div className="mt-2 ml-4 w-fit space-y-2 p-2 px-4 overflow-hidden overflow-x-auto border border-dinoBotRed bg-dinoBotRed text-white font-bold rounded-3xl rounded-tl-none">
                {content}
            </div>
        </div>
    )
}
