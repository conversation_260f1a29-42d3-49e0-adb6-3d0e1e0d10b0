'use client'
import React from 'react'
import { motion } from 'framer-motion'
import {
    IconBookOutline,
    IconEMC,
    IconGeo,
    IconHistory,
    IconPI,
    IconPhysic,
    IconSVT
} from '@/components/ui/icons'
import useTopicStore from '@/lib/stores/topic-store/topic-store'
import { useCookies } from 'next-client-cookies'
import useCtrlModeStore from '@/app/[locale]/(app)/(mode)/controle/store/controle-store'
import { Domain } from '@prisma/client'

function CategoryChoser() {
    const setSubject = useCtrlModeStore(state => state.setSubject)
    const setTopic = useTopicStore(state => state.setTopic)
    const cookies = useCookies()

    const handleSubjectChange = (subject: string) => {
        setSubject(subject)
        setTopic(JSON.parse(subject) as Domain)
        cookies.set('topic', subject)
    }

    return (
        <motion.div
            initial={{ x: 1000, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: -1000, opacity: 0 }}
            transition={{ duration: 0.5 }}
            className="relative flex flex-col justify-center items-center gap-8 sm:gap-11 bg-transparent"
        >
            <h2 className="text-center text-2xl sm:text-5xl font-extrabold underline text-dinoBotVibrantBlue">
                Choisis une Matière
            </h2>
            {/* <div className="w-60 sm:size-auto grid grid-cols-2 grid-rows-4 sm:grid-cols-4 sm:grid-rows-2 gap-5 "> */}
            <div className="w-60 sm:size-auto flex flex-col gap-5 ">
                <div className="flex gap-5 flex-col sm:flex-row sm:justify-center">
                    <div className="flex gap-5 justify-center">
                        <motion.div
                            whileHover={{
                                scale: 1.1,
                                transition: {
                                    type: 'spring',
                                    stiffness: 1000,
                                    damping: 10
                                }
                            }}
                            onClick={() => handleSubjectChange('Mathématiques')}
                            className="relative size-28 bg-dinoBotRed flex flex-col justify-center items-center  rounded-xl   cursor-pointer "
                        >
                            <IconPI className="size-12 mb-3" />
                            <div className="text-xs font-extrabold text-white absolute bottom-3">
                                Mathematique
                            </div>
                        </motion.div>
                    </div>
                    <div className="flex gap-5">
                        <motion.div
                            whileHover={{
                                scale: 1.1,
                                transition: {
                                    type: 'spring',
                                    stiffness: 1000,
                                    damping: 10
                                }
                            }}
                            onClick={() => handleSubjectChange('Français')}
                            className="relative size-28 bg-dinoBotSky flex flex-col justify-center items-center  rounded-xl  cursor-pointer "
                        >
                            <IconBookOutline className="size-12 mb-3 fill-none stroke-white" />
                            <div className="text-xs font-extrabold text-white absolute bottom-3">
                                Français
                            </div>
                        </motion.div>
                        <motion.div
                            whileHover={{
                                scale: 1.1,
                                transition: {
                                    type: 'spring',
                                    stiffness: 1000,
                                    damping: 10
                                }
                            }}
                            onClick={() =>
                                handleSubjectChange('Physique-Chimie')
                            }
                            className="relative size-28 bg-dinoBotCyan flex flex-col justify-center items-center  rounded-xl   cursor-pointer "
                        >
                            <IconPhysic className="size-14 mb-3 stroke-none fill-white" />
                            <div className="text-xs font-extrabold text-white absolute bottom-3">
                                Physique-Chimie
                            </div>
                        </motion.div>
                    </div>
                </div>
                <div className="flex gap-5 flex-col sm:flex-row sm:justify-center">
                    <div className="flex gap-5">
                        <motion.div
                            // whileHover={{
                            //   scale: 1.1,
                            //   transition: {
                            //     type: 'spring',
                            //     stiffness: 1000,
                            //     damping: 10
                            //   }
                            // }}
                            // onClick={() => handleSubjectChange('SVT')}
                            className="relative size-28 bg-dinoBotLightGray flex flex-col justify-center items-center  rounded-xl  cursor-not-allowed "
                            // className="relative size-28 bg-dinoBotGreen flex flex-col justify-center items-center  rounded-xl  cursor-pointer "
                        >
                            <IconSVT className="size-12 mb-3 fill-none stroke-white" />
                            <div className="text-xs font-extrabold text-white absolute bottom-3">
                                SVT
                            </div>
                        </motion.div>
                        <motion.div
                            // whileHover={{
                            //   scale: 1.1,
                            //   transition: {
                            //     type: 'spring',
                            //     stiffness: 1000,
                            //     damping: 10
                            //   }
                            // }}
                            // onClick={() => handleSubjectChange('EMC')}
                            className="relative size-28 bg-dinoBotLightGray flex flex-col justify-center items-center  rounded-xl  cursor-not-allowed "
                            // className="relative size-28 bg-dinoBotRoseBonbon flex flex-col justify-center items-center  rounded-xl  cursor-pointer "
                        >
                            <IconEMC className="size-12 mb-3 fill-none stroke-white" />
                            <div className="text-xs font-extrabold text-white absolute bottom-3">
                                EMC
                            </div>
                        </motion.div>
                    </div>
                    <div className="flex gap-5 justify-center">
                        <motion.div
                            // whileHover={{
                            //   scale: 1.1,
                            //   transition: {
                            //     type: 'spring',
                            //     stiffness: 1000,
                            //     damping: 10
                            //   }
                            // }}
                            // onClick={() => handleSubjectChange('Géographie')}
                            className="relative size-28 bg-dinoBotLightGray flex flex-col justify-center items-center  rounded-xl  cursor-not-allowed "
                            // className="relative size-28 bg-dinoBotPurple flex flex-col justify-center items-center  rounded-xl cursor-pointer "
                        >
                            <IconGeo className="size-12 mb-3 stroke-white" />
                            <div className="text-xs font-extrabold text-white absolute bottom-3">
                                Géographie
                            </div>
                        </motion.div>
                        <motion.div
                            // whileHover={{
                            //   scale: 1.1,
                            //   transition: {
                            //     type: 'spring',
                            //     stiffness: 1000,
                            //     damping: 10
                            //   }
                            // }}
                            // onClick={() => handleSubjectChange('Histoire')}
                            className="relative size-28 bg-dinoBotLightGray flex flex-col justify-center items-center  rounded-xl  cursor-not-allowed "
                            // className="relative size-28 bg-dinoBotYellow flex flex-col justify-center items-center  rounded-xl  cursor-pointer "
                        >
                            <IconHistory className="size-12 mb-3 fill-none stroke-white" />
                            <div className="text-xs font-extrabold text-white absolute bottom-3">
                                Histoire
                            </div>
                        </motion.div>
                    </div>
                </div>
            </div>
        </motion.div>
    )
}

export default CategoryChoser
