import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import { Rocket } from 'lucide-react'
import { useTranslations } from 'next-intl'
import React, { useEffect, useState } from 'react'

type BetaPopupProps = { children: React.ReactNode; feature: string }

const BetaPopup = ({ children, feature }: BetaPopupProps) => {
    const [isOpen, setIsOpen] = useState(false)
    const translate = useTranslations('app.mode.train.beta')
    const betaFeature = `viewed_beta_${feature}`

    useEffect(() => {
        const isOpened = localStorage.getItem(betaFeature)
        if (!!isOpened) {
            setIsOpen(isOpened == 'true' ? false : true)
        } else {
            localStorage.setItem(betaFeature, 'false')
            setIsOpen(true)
        }
    }, [])

    function handleClose() {
        setIsOpen(false)
        localStorage.setItem(`viewed_beta_${feature}`, 'true')
    }

    function handleOpen() {
        const isOpened = localStorage.getItem(betaFeature)
        if (isOpened == 'true') return
        setIsOpen(true)
    }

    return (
        <Dialog open={isOpen} onOpenChange={handleOpen}>
            <DialogTrigger>{children}</DialogTrigger>
            <DialogContent className="[&>button>svg]:hidden">
                <div>
                    {' '}
                    {translate('message')}{' '}
                    <Rocket className="inline" color="red" /> <br />{' '}
                    {translate('description')}
                </div>
                <div className="flex items-center">
                    <Button
                        onClick={() => handleClose()}
                        className="w-1/4 mx-auto bg-dinoBotBlue hover:bg-dinoBotVibrantBlue"
                    >
                        ok
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    )
}

export default BetaPopup
