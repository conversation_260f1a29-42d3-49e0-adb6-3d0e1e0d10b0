import { cn } from '@/lib/utils/utils'
import React, { useEffect, useState } from 'react'
import { selectorNotationStore } from '../../../stores/notation.store'
import 'moment/locale/fr'
import 'moment/locale/ar'
import 'moment/locale/en-gb'
import moment from 'moment'
import { useLocale, useTranslations } from 'next-intl'
import { getNotationMinMaxBycontroleId } from '@/lib/evaluation-scheduler/actions'

const ScoringDetails = () => {
    const locale = useLocale()
    moment.locale(locale)
    const t = useTranslations('app.courses.notation')
    const selectedPlannedEvaluation =
        selectorNotationStore.use.selectedPlannedEvaluations()
    const [{ min, max }, setMinMax] = useState<{ min: number; max: number }>({
        min: 0,
        max: 0
    })

    useEffect(() => {
        ;(async () => {
            const data = await getNotationMinMaxBycontroleId(
                selectedPlannedEvaluation?.control?.id ?? ''
            )
            if ('error' in data) throw new Error(data.error)
            setMinMax({
                min: data._min.globalScore!,
                max: data._max.globalScore!
            })
        })()
    }, [selectedPlannedEvaluation])
    return (
        <div className="flex-[3] h-full flex overflow-hidden">
            <div
                className={cn(
                    'w-0 h-full transition-all duration-300 ease-in-out flex flex-col gap-4 overflow-hidden ',
                    selectedPlannedEvaluation && 'w-full px-4'
                )}
            >
                <div className="text-dinoBotBlue">
                    <h3 className="font-medium text-xl">
                        {selectedPlannedEvaluation?.title}
                    </h3>
                    <p>
                        {moment(
                            selectedPlannedEvaluation?.submitCorrectedAt
                        ).format('DD MMMM YYYY')}
                    </p>
                </div>
                <div className="grid grid-cols-2 grid-rows-4">
                    <h4 className="row-span-2 font-medium text-lg text-dinoBotBlackBlue">
                        {t('note_student')}
                    </h4>
                    <h4 className="row-span-2 font-medium text-lg text-dinoBotBlackBlue">
                        {
                            selectedPlannedEvaluation?.studentSubmission?.at(0)
                                ?.globalScore
                        }
                    </h4>
                    <h4 className="row-start-3 text-lg text-dinoBotBlackBlue">
                        {t('note_max')}
                    </h4>
                    <h4 className="row-start-3 text-lg text-dinoBotBlackBlue">
                        {max}
                    </h4>
                    <h4 className="row-start-4 text-lg text-dinoBotBlackBlue">
                        {t('note_min')}
                    </h4>
                    <h4 className="row-start-4 text-lg text-dinoBotBlackBlue">
                        {min}
                    </h4>
                </div>
                <h6 className="text-sm text-dinoBotDarkGray">
                    {t('note_eval')}{' '}
                    {selectedPlannedEvaluation?.control?.letterRange}
                </h6>
            </div>
            <div
                className={cn(
                    'size-full transition-all duration-300 ease-in-out flex items-center justify-center overflow-hidden',
                    selectedPlannedEvaluation && 'w-0'
                )}
            >
                <p className="text-dinoBotDarkGray">{t('select')}</p>
            </div>
        </div>
    )
}

export default ScoringDetails
