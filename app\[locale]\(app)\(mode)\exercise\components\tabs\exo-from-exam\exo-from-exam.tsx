//Hooks
import React, { useEffect, useMemo, useState } from 'react'
import { usePathname, useRouter } from '@/i18n/routing'
import { useCookies } from 'next-client-cookies'
import { useLocale, useTranslations } from 'next-intl'

//UI
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import InfoTooltip from '@/components/ui/info-tooltip'
import { Slider } from '@/components/ui/slider'
import { toast } from 'sonner'
import ErrorTooltip from '@/components/ui/error-tooltip'
import MultipleSelect from 'react-select'

//Store
import useExoModeStore, {
    selectUseExoModeStore
} from '@/lib/stores/exercise-mode-store/exercise-store'

//API-Function-Call
import {
    ExoOutput,
    ExoSortsAndFilters,
    ExoSubject,
    getExamsByDomainIdAndLevelIdAndSubjects
} from '@/lib/exams/actions'

//Components
import ExoFromExamTableLayout from './exo-from-exam-table-layout'
import useAccountStore from '@/lib/stores/account-store/store'
import { Domain } from '@prisma/client'
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import { getLangProps } from '@/lib/utils/string.utils'

interface ExoFromDbProps {
    domains: Domain[]
    getSubjectsByDomainIdAndLevelId: (
        domainId: number,
        levelId: number | null
    ) => Promise<ExoSubject[]>
}

function ExoFromExam({
    domains,
    getSubjectsByDomainIdAndLevelId
}: ExoFromDbProps) {
    const [selectedSubject, setSelectedSubject] = useState<string[]>([])
    const [exams, setExams] = useState<ExoOutput[]>([])
    const [subjects, setSubjects] = useState<ExoSubject[]>([])
    const formData = selectUseExoModeStore.use.exoInfoFromExam()
    const handleFormChange = selectUseExoModeStore.use.updateExoInfoFromExam()
    const [showError, setShowError] = useState<boolean>(false)
    const setMode = useExoModeStore(state => state.setMode)
    const t = useTranslations('app.mode.exo.tab.exam')
    const t2 = useTranslations('app.chat.exam-dialog')
    const t3 = useTranslations('app.mode.exo.tab.db')
    const router = useRouter()
    const path = usePathname()
    const cookies = useCookies()
    const topic = cookies.get('topic')
    const topicId = cookies.get('topicId')
    const { user } = useAccountStore()
    const lang = useLocale()

    useEffect(() => {
        setMode('FROM_EXAM')
    }, [])

    useEffect(() => {
        setSelectedSubject([])
        setExams([])
        if (formData.domainId && formData.domainId.length > 0 && user.levelId) {
            ;(async () => {
                const subjs = await getSubjectsByDomainIdAndLevelId(
                    Number.parseInt(formData.domainId),
                    user.levelId
                )
                setSubjects(subjs)
            })()
        } else {
            setSubjects([])
        }
    }, [formData.domainId, user.levelId])

    const changeSubjects = async () => {
        const test = await getExamsByDomainIdAndLevelIdAndSubjects(
            filteredSubjectId,
            user.levelId,
            Number.parseInt(formData.domainId!)
        )

        setExams(test)
    }

    const filteredSubjectId = useMemo(() => {
        return subjects
            .filter(subject => selectedSubject.includes(subject.name))
            .map(subject => subject.id)
    }, [selectedSubject, subjects])

    useEffect(() => {
        if (selectedSubject.length > 0 && formData.domainId) {
            changeSubjects()
        } else {
            setExams([])
        }
    }, [selectedSubject, formData.domainId])

    const handleQuestionNumberChange = (value: number) => {
        try {
            // const int = parseInt(value)
            if (value > 10) {
                handleFormChange('qstNbr', 10)
                toast.info(t('n10'), { duration: 2500 })
            } else if (value < 1) {
                handleFormChange('qstNbr', 1)
                toast.info(t('n1'), { duration: 2500 })
            } else {
                handleFormChange('qstNbr', value)
            }
        } catch (error) {
            console.log(error)
            handleFormChange('qstNbr', 5)
        }
    }

    useEffect(() => {
        if (formData.qstNbr) handleQuestionNumberChange(formData.qstNbr)
    }, [formData.qstNbr])

    const submit = () => {
        if (formData.examId) {
            if (formData.qstNbr) {
                setShowError(false)
                setMode('FROM_EXAM')
                router.push(`/${path}/train`)
            } else {
                toast.error(t('n1&10'))
            }
        } else {
            setShowError(true)
            toast.info(t('n10'))
        }
    }

    return (
        <div className="flex flex-col gap-9 w-full">
            <div className="flex flex-row gap-2 w-full">
                <div className="w-24 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                    {t3('subject.name')}{' '}
                </div>
                <Select
                    value={formData.domainId}
                    onValueChange={value => handleFormChange('domainId', value)}
                >
                    <SelectTrigger className="max-w-full">
                        <SelectValue placeholder={t3('subject.placeholder')} />
                    </SelectTrigger>
                    <SelectContent
                        className={`${domains.length > 5 ? 'h-48' : 'h-fit'}`}
                    >
                        <SelectGroup>
                            {domains.map(domain => (
                                <SelectItem
                                    key={domain.id}
                                    value={domain.id.toString()}
                                >
                                    {getLangProps({
                                        obj: domain,
                                        base: 'name',
                                        lang
                                    })}
                                </SelectItem>
                            ))}
                        </SelectGroup>
                    </SelectContent>
                </Select>
            </div>
            <div className="w-full flex flex-row gap-2">
                <div className="w-24 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                    {t2('categorie')}{' '}
                </div>
                <MultipleSelect
                    isMulti
                    className="w-full"
                    options={subjects.map(sub => ({
                        label: sub.name,
                        value: sub.name
                    }))}
                    placeholder={t('chois')}
                    onChange={value => {
                        setSelectedSubject(value.map(v => v.value))
                    }}
                    value={selectedSubject.map(v => ({
                        label: v,
                        value: v
                    }))}
                    isDisabled={!formData.domainId}
                />
            </div>

            {exams.length > 0 && (
                <>
                    <div className="flex flex-col gap-1">
                        <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                            {t('exam')}{' '}
                            {(formData.examId.length <= 0 && !showError) ||
                            formData.examId.length > 0 ? (
                                <InfoTooltip message={t('chois-savoir')} />
                            ) : (
                                <ErrorTooltip
                                    message={t('error-chois-savoir')}
                                />
                            )}
                        </div>
                        <div>
                            <p className="text-[10px] text-[#707070]">
                                {t('chois-savoir-text')}
                            </p>
                            <p className="text-[10px] font-light text-[#707070]">
                                {exams.length} {t('result')}
                            </p>
                        </div>
                        <div className="mt-2">
                            <ExoFromExamTableLayout exams={exams} />
                        </div>
                    </div>
                    <div className="w-full flex flex-col md:flex-row gap-4 md:gap-12">
                        {/* <div className='w-full sm:w-2/5'>
              <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                Nombre de questions
              </div>
              <Input type="number" value={formData.qstNbr} min={1} max={10} className='h-9 rounded-xl' onChange={(e)=>handleFormChange("qstNbr",e.target.value)}/>
            </div> */}
                        <div className="w-full sm:w-2/5">
                            <div className="flex gap-1 items-center justify-between text-sm font-bold text-dinoBotDarkGray">
                                {t('difficulty')}{' '}
                                <span>{formData.difficulty}/3</span>
                            </div>
                            <div className="py-3">
                                <Slider
                                    defaultValue={[formData.difficulty]}
                                    min={0}
                                    max={3}
                                    step={1}
                                    className={'w-full '}
                                    onValueChange={value =>
                                        handleFormChange('difficulty', value[0])
                                    }
                                />
                            </div>
                        </div>
                    </div>
                    <p className="text-[10px] text-[#707070]">{t('have')}</p>
                </>
            )}

            <div className="flex flex-col gap-1">
                <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                    {' '}
                    {t('prompt')}{' '}
                    <span className="font-normal">{t('opt')}</span>{' '}
                </div>
                <div className="mt-2">
                    <Textarea
                        placeholder={t('rid')}
                        value={formData.customPrompt}
                        onChange={e => {
                            if (e.target.value.length <= 1000)
                                handleFormChange('customPrompt', e.target.value)
                        }}
                    ></Textarea>
                    <p className="text-xs text-right mt-1 text-dinoBotGray">
                        {formData.customPrompt.length ?? 0}/1000{' '}
                        {t('caracteres')}
                    </p>
                </div>
            </div>
            <div className="w-full flex justify-center items-center mt-2">
                <Button
                    className="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/90 rounded-xl w-36"
                    onClick={submit}
                >
                    {t('submit')}
                </Button>
            </div>
        </div>
    )
}

export default ExoFromExam
