import React, { useMemo } from 'react'
import { selectTasks } from '../store/tasks.store'
import { selectUseContentStore } from '../../store/content.store'
import { updateTaskActiveStatus } from '@/lib/praxeo/actions'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import IconeSwitch from '@/components/shared/ui/icone-switch'
import { Eye, EyeOff, LoaderCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'

type PartWithData = {
    id: string
    name: string
    tasks: TaskWithCompetencies[]
}

type TaskWithCompetencies = {
    id: number
    description: string
    isDisabled: boolean
    competencies: { description: string | null }[]
}

interface ListTasksProps {
    part: PartWithData
}

const ListTasks = ({ part }: ListTasksProps) => {
    const searchFilter = selectTasks.use.searchFilter()
    const domainId = selectUseContentStore.use.domainId()
    const levelId = selectUseContentStore.use.levelId()
    const tasks = part.tasks
    const queryClient = useQueryClient()

    const filteredTasks = useMemo(() => {
        if (!searchFilter.trim()) return tasks

        return tasks?.filter(task =>
            task.description.toLowerCase().includes(searchFilter.toLowerCase())
        )
    }, [tasks, searchFilter])

    const refetchData = () => {
        // Invalider le cache pour refetch les données
        queryClient.invalidateQueries({
            queryKey: ['chapters-with-tasks', domainId, levelId, searchFilter]
        })
    }

    if (!part) {
        return (
            <div className="text-center">Veuillez sélectionner une partie</div>
        )
    }

    if (!tasks || tasks.length === 0) {
        return <div className="text-center">Aucune tâche disponible</div>
    }

    return (
        <div className="flex flex-col gap-3">
            {filteredTasks?.length! > 0 ? (
                filteredTasks?.map(task => (
                    <ItemTask
                        key={task.id}
                        task={task}
                        queryRefetch={refetchData}
                    />
                ))
            ) : (
                <div className="text-center text-gray-500">
                    {searchFilter
                        ? 'Aucune tâche trouvée pour cette recherche'
                        : 'Aucune tâche disponible'}
                </div>
            )}
        </div>
    )
}

export default ListTasks

const ItemTask = ({
    task,
    queryRefetch
}: {
    task: { id: number; description: string; isDisabled: boolean }
    queryRefetch: () => void
}) => {
    const updateTaskMutation = useMutation({
        mutationFn: ({
            taskId,
            isDisabled
        }: {
            taskId: number
            isDisabled: boolean
        }) => updateTaskActiveStatus(taskId, isDisabled),
        onSuccess: () => queryRefetch(),
        onError: (error: any) =>
            toast.error('Erreur lors de la mise à jour: ' + error.message)
    })

    const handleToggleActive = () => {
        const newStatus = !task.isDisabled
        updateTaskMutation.mutate({ taskId: task.id, isDisabled: newStatus })
    }

    return (
        <div className="px-2 pl-4 py-1 rounded-md bg-dinoBotLightGray/45 flex items-center justify-between text-xs">
            <h3 className="">{task.description}</h3>
            <Button
                variant="link"
                onClick={handleToggleActive}
                disabled={updateTaskMutation.isPending}
            >
                {updateTaskMutation.isPending &&
                updateTaskMutation.variables?.taskId === task.id ? (
                    <LoaderCircle className="h-5 w-5 animate-spin" />
                ) : (
                    <IconeSwitch
                        isActive={
                            updateTaskMutation.variables?.taskId === task.id
                                ? updateTaskMutation.variables.isDisabled
                                : (task.isDisabled ?? true)
                        }
                        iconEnable={<EyeOff />}
                        iconDisable={<Eye />}
                    />
                )}
            </Button>
        </div>
    )
}
