'use client'
import React, { useEffect, useState, useTransition } from 'react'
import { Check, Copy } from 'lucide-react'
import { selectUseContentStore } from '../../store/content.store'
import { addOrUpdateEvaluationPrompt } from '../../../../../../lib/evaluation-prompts/actions'
import { EvaluationType } from '@prisma/client'
import { toast } from 'sonner'
import { EvaluationPromptPartial } from '../../../../../../prisma/generated/zod/modelSchema/EvaluationPromptSchema'
import EvalDialog from './eval-dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'

type EvalInputProps = {
    title: string
    placeholder?: string
    prompt?: string
    evalPrompt?: EvaluationPromptPartial
}
export const EvalInput = ({
    title,
    evalPrompt,
    placeholder,
    prompt
}: EvalInputProps) => {
    const [copied, setCopied] = useTransition()
    const [content, setContent] = useState<string | undefined>(prompt)

    const domain = selectUseContentStore.use.domain()
    const level = selectUseContentStore.use.level()
    const levelDomainId = selectUseContentStore.use.levelDomainId()

    const translatedTitle = {
        [EvaluationType.FORMATIVE]: 'Formative',
        [EvaluationType.SUMMATIVE]: 'Sommative',
        [EvaluationType.DIAGNOSTIC]: 'Diagnostique',
        [EvaluationType.CERTIFYING]: 'Certifiante'
    }

    useEffect(() => {
        setContent('')
        if (evalPrompt) setContent(evalPrompt?.prompt)
    }, [levelDomainId, evalPrompt])

    const handleSavePrompt = async () => {
        try {
            if (evalPrompt?.prompt === content) return

            const response = await addOrUpdateEvaluationPrompt({
                title: title as EvaluationType,
                prompt: content,
                levelDomainId: levelDomainId
            })

            if ('error' in response) {
                throw new Error(response.error)
            }

            if (evalPrompt) {
                evalPrompt.prompt = response?.prompt as string
            }
            toast.success(title + ' Prompt mis à jour')
            setContent(response.prompt)
        } catch (error) {
            console.error('Error saving prompt:', error)
            toast.error('Erreur lors de la mise à jour du prompt')
        }
    }

    const handleCopyToClipboard = (e: React.MouseEvent) => {
        e.preventDefault()
        setCopied(() => {
            try {
                navigator.clipboard.writeText(content as string)
                toast.success('Contenu copié dans le presse-papier')
            } catch (error) {
                console.error('Error copying to clipboard:', error)
            }
        })
    }

    // Determine the display title - use translated title if type is provided, otherwise use the original title

    return (
        <article className=" h-full">
            <header className=" py-2 flex justify-between w-5/6 items-center">
                <Label className="font-inter font-bold text-dinoBotDarkGray size-16 h-fit">
                    {translatedTitle[title as EvaluationType]}
                </Label>
                <EvalDialog
                    title={`Prévisualisation des exercices - ${domain.name} ${level.name} (${translatedTitle[title as EvaluationType]})`}
                    type={title as EvaluationType}
                    evalPrompt={content!}
                />
            </header>
            <section className="h-1/3 flex flex-row">
                <Textarea
                    className="w-5/6"
                    placeholder={placeholder}
                    onChange={e => {
                        setContent(e.target.value)
                    }}
                    onBlur={handleSavePrompt}
                    value={content}
                ></Textarea>
                <aside className="w-1/6 h-full px-4">
                    <Button
                        onClick={handleCopyToClipboard}
                        variant={'ghost'}
                        className="p-0 flex justify-center items-center size-8"
                    >
                        {copied ? (
                            <Check className="text-dinoBotCyan" size={16} />
                        ) : (
                            <Copy
                                className="cursor-pointer text-dinoBotGray"
                                size={16}
                            />
                        )}
                    </Button>
                </aside>
            </section>
        </article>
    )
}
