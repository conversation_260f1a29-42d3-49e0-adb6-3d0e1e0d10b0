'use client'
import { useQuery } from '@tanstack/react-query'
import React from 'react'
import { selectUseContentStore } from '../../store/content.store'
import { getChaptersWithPartsAndFilteredSkills } from '@/lib/skills/actions'
import { MatchQuery } from '@/components/match-query/match-query'
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger
} from '@/components/ui/accordion'
import { selectSkillsStore } from '../store/skills.store'
import SkillPartsList from './skill-parts-list'
import { useTranslations, useLocale } from 'next-intl'
import { getLangProps } from '@/lib/utils/string.utils'

const SkillChapterList = () => {
    const t = useTranslations('establishment.manage_content.skills')
    const locale = useLocale()
    const domainId = selectUseContentStore.use.domainId()
    const levelId = selectUseContentStore.use.levelId()
    const searchFilter = selectSkillsStore.use.searchFilter()
    const setChaptersWithData = selectSkillsStore.use.setChaptersWithData()

    const chapterQuery = useQuery({
        queryKey: ['chapters-with-skills', domainId, levelId, searchFilter],
        queryFn: async () => {
            if (!domainId || !levelId) return []
            return getChaptersWithPartsAndFilteredSkills(
                domainId,
                levelId,
                searchFilter || undefined
            )
        },
        enabled: !!domainId && !!levelId
    })

    React.useEffect(() => {
        if (chapterQuery.data && Array.isArray(chapterQuery.data)) {
            setChaptersWithData(chapterQuery.data)
        }
    }, [chapterQuery.data, setChaptersWithData])

    return (
        <MatchQuery
            value={chapterQuery}
            success={chaptersWithData => {
                if (!Array.isArray(chaptersWithData)) {
                    return (
                        <div className="text-center text-red-500">
                            Erreur lors du chargement des données
                        </div>
                    )
                }

                const chaptersWithSkills = chaptersWithData.filter(
                    (chapter: any) => {
                        const totalSkills = chapter.parts.reduce(
                            (acc: number, part: any) =>
                                acc + part.skills.length,
                            0
                        )
                        return totalSkills > 0
                    }
                )

                if (chaptersWithSkills.length === 0) {
                    return (
                        <div className="text-center text-gray-500">
                            {t('no_chapters_with_skills')}
                        </div>
                    )
                }

                return (
                    <Accordion type="multiple" className="flex flex-col gap-2">
                        {chaptersWithSkills.map((chapter: any) => (
                            <AccordionItem
                                key={chapter.id}
                                value={chapter.id}
                                className="border-b-0"
                            >
                                <AccordionTrigger
                                    className="rounded-md bg-dinoBotLightGray/45 px-2 "
                                    iconClassName="size-6"
                                >
                                    <div className="flex items-center justify-start gap-1">
                                        <span className="font-semibold">
                                            {t('chapter_label')}
                                        </span>{' '}
                                        {getLangProps({
                                            obj: chapter,
                                            base: 'title',
                                            lang: locale
                                        })}
                                        <span className="text-sm text-gray-500">
                                            (
                                            {chapter.parts.reduce(
                                                (acc: number, part: any) =>
                                                    acc + part.skills.length,
                                                0
                                            )}{' '}
                                            {t('skills_count')})
                                        </span>
                                    </div>
                                </AccordionTrigger>
                                <AccordionContent className="px-3">
                                    <SkillPartsList chapter={chapter} />
                                </AccordionContent>
                            </AccordionItem>
                        ))}
                    </Accordion>
                )
            }}
            loading={() => (
                <div className="text-center">{t('loading_chapters')}</div>
            )}
            error={error => (
                <div className="text-center">
                    {t('loading_error')} {error.message}
                </div>
            )}
            empty={() => (
                <div className="text-center">{t('no_chapters_available')}</div>
            )}
            inactive={() => (
                <div className="text-center">{t('select_level_domain')}</div>
            )}
        />
    )
}

export default SkillChapterList
