import { usePathname, useRouter } from '@/i18n/routing'
import { selectUseAvatarStore } from '@/lib/stores/avatar-store/avatar-store'
import useChatStore from '@/lib/stores/chat-store/chat-store'
import useStreamingStore from '@/lib/stores/text-streaming-store/store'
import { Session } from '@/lib/types'
import { Message, UIMessage } from 'ai'
import { nanoid } from 'nanoid'
import { useCookies } from 'next-client-cookies'
import { useLocale, useTranslations } from 'next-intl'
import React, { useEffect } from 'react'

interface customUiMessage extends UIMessage {
    file: unknown
}

type CheckMessageChatProps = {
    messages: UIMessage[]
    session?: Session
    setMessages: (
        messages: Message[] | ((messages: Message[]) => Message[])
    ) => void
    id: string
    previousPath: string
    setPreviousPath: (previousPath: string) => void
    status: 'error' | 'submitted' | 'streaming' | 'ready'
}

const CheckMessageChat = ({
    messages,
    session,
    setMessages,
    id,
    previousPath,
    setPreviousPath,
    status
}: CheckMessageChatProps) => {
    const t = useTranslations('app.chat.[id]')
    const router = useRouter()
    const setKnowledgeBaseModeContent =
        selectUseAvatarStore.use.setKnowledgeBaseModeContent()
    const { setIsStreaming } = useStreamingStore()
    const cookies = useCookies()
    const locale = useLocale()
    const {
        refreshChatsList,
        setRefreshChatsList,
        refreshFilesList,
        setRefreshFilesList
    } = useChatStore()
    const path = usePathname()

    useEffect(() => {
        const messagesLength = messages?.length
        const includesFile = (messages[messagesLength - 2] as customUiMessage)
            ?.file
        if (
            messagesLength > 0 &&
            messagesLength % 2 === 0 &&
            !refreshChatsList
        ) {
            setRefreshChatsList(true)
            if (includesFile && !refreshFilesList) {
                setRefreshFilesList(true)
            }
        }
        setKnowledgeBaseModeContent(
            `voicie le chat : ${JSON.stringify(messages)}`
        )
        if (messagesLength === 4 && !session?.user) {
            cookies.set('restrictFreeUser', 'yes')
            setIsStreaming(false)
            setMessages(currentMessages => [
                ...currentMessages,
                {
                    id: nanoid(),
                    role: 'assistant',
                    content: t('continue')
                }
            ])
        }
    }, [messages])

    useEffect(() => {
        if (session?.user) {
            if (previousPath.includes('chat') && path === '/') {
                setMessages([])
                router.push('/')
                router.refresh()
            }
            if (
                !path.includes('chat') &&
                messages.length === 2 &&
                status === 'ready'
            ) {
                setTimeout(() => {
                    if (cookies.get('feature') === 'Chat')
                        window.history.replaceState(
                            {},
                            '',
                            `${locale}/chat/${id}`
                        )
                    if (cookies.get('feature') === 'Exam')
                        window.history.replaceState({}, '', `exam/chat/${id}`)
                }, 3000)
            }
        }
        setPreviousPath(path)
    }, [id, session?.user, messages, previousPath, status])

    return <></>
}

export default CheckMessageChat
