'use client'
import { getStudentClassById } from '@/lib/control-mode/services/class/actions'
import { ClassWithPartialRelations } from '@/prisma/generated/zod/modelSchema/ClassSchema'
import { useTranslations } from 'next-intl'
import React, { useEffect } from 'react'

type CourseDetailsCardProps = {
    id?: string
}

function CourseDetailsCard({ id }: CourseDetailsCardProps) {
    const [course, setCourse] = React.useState<ClassWithPartialRelations>()
    const translate = useTranslations('app.courses.index')

    useEffect(() => {
        ;(async () => {
            const course = await getStudentClassById(id!)
            setCourse(course!)
        })()
    }, [])

    return (
        <div
            className="h-fit w-full p-6 m-2 rounded-sm font-medium text-dinoBotWhite text-xl flex gap-2 items-center "
            style={{ backgroundColor: course?.classColor as string }}
        >
            <div className="flex flex-col">
                <h1 className="font-bold text-xl">{course?.name}</h1>
                <h2 className="font-light text-sm">
                    {translate('card.title')}
                </h2>
                <span>
                    {course?.mainTeacher?.lastName}{' '}
                    {course?.mainTeacher?.firstName}
                </span>
            </div>
        </div>
    )
}

export default CourseDetailsCard
