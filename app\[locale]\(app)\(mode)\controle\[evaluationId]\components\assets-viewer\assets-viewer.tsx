'use client'

import { useState } from 'react'
import VideoPlayer from './video-player'
import ImageViewer from './image-viewer'
import { selectUseCtrlMediaStore } from '../../../store/controle-media-store'
import { Download } from 'lucide-react'
import { ControlQuestionMedia } from '@/lib/control-mode/types/control/types'
import Asset from './asset'

interface Props {
    attachments: ControlQuestionMedia[]
}

function AssetsViewer({ attachments }: Props) {
    const [viewMode, setViewMode] = useState<'scroll' | 'slider'>('scroll')
    const setAttachments = selectUseCtrlMediaStore().setAttachments
    return (
        <div className="w-full  flex flex-wrap">
            <div
                className={`flex  gap-1`}
                style={{
                    scrollSnapType:
                        viewMode === 'slider' ? 'x mandatory' : 'none'
                }}
            >
                {attachments.map((file, index) => (
                    <Asset
                        key={file.id}
                        file={file}
                        onClick={() => setAttachments(attachments, index)}
                    />
                ))}
            </div>
        </div>
    )
}

export default AssetsViewer
