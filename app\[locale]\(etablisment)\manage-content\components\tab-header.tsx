'use client'
import { Separator } from '@/components/ui/separator'
import { usePathname } from '@/i18n/routing'
import { useTranslations } from 'next-intl'
import Link from 'next/link'
import React from 'react'

const TabHeader = () => {
    const t = useTranslations('establishment.manage_content.tabs')
    const pathname = usePathname()
    const path = (path: string) => `/manage-content/${path}`
    const isActive = (href: string) => pathname === href
    return (
        <div className="flex rounded-tl-lg border-x-2 border-dinoBotGray border-t-2 ">
            <Link
                href={path('skills')}
                className={`py-2 px-4 ${isActive(path('skills')) ? 'bg-dinoBotDarkRed text-dinoBotWhite border-none' : ''}`}
            >
                {t('skills')}
            </Link>
            <Separator orientation="vertical" className="h-10" />
            <Link
                href={path('tasks')}
                className={`py-2 px-4 ${isActive(path('tasks')) ? 'bg-dinoBotDarkRed text-dinoBotWhite border-none' : ''}`}
            >
                {t('tasks')}
            </Link>
            <Separator orientation="vertical" className="h-10" />
            <Link
                href={path('eval')}
                className={`py-2 px-4 ${isActive(path('eval')) ? 'bg-dinoBotDarkRed text-dinoBotWhite border-none' : ''}`}
            >
                {t('eval')}
            </Link>
        </div>
    )
}

export default TabHeader
