'use client'
import React, { useEffect, useRef, useState } from 'react'
import ReactQuill from 'react-quill-new'
import * as katex from 'katex'
import 'react-quill-new/dist/quill.snow.css'
import useCortexStore from '@/app/[locale]/(app)/(mode)/controle/store/control-cortex-store'
window.katex = katex

const modules = {
    toolbar: [
        [{ header: '1' }, { header: '2' }, { font: [] }],
        [{ size: [] }],
        ['bold', 'italic', 'underline', 'strike', 'blockquote'],
        [{ list: 'ordered' }, { indent: '-1' }, { indent: '+1' }],
        ['link', 'image', 'video', 'formula'],
        ['clean']
    ]
}

interface Props {
    theme: string
    placeholder?: string
    disable: boolean
    answerid: number
    contentid: number
    onChange: (value: string) => void
    className?: string
    value?: string
}

function QuillTextArea({
    theme = 'core',
    placeholder = 'Write something...',
    onChange,
    answerid,
    contentid,
    disable,
    className,
    value
}: Props) {
    const [formats] = useState([
        'header',
        'font',
        'size',
        'bold',
        'italic',
        'underline',
        'strike',
        'blockquote',
        'list',
        'indent',
        'link',
        'image',
        'video',
        'align',
        'formula' // Make sure 'align' is included here as well
    ])
    const quillRef = useRef<ReactQuill>(null)
    const { cortexValue, insert, idanswer, idcontent, setText } =
        useCortexStore()
    const [, setLocalValue] = useState('')
    const getText = (contents: { insert: { formula: string } }[]) => {
        let text = ''
        contents.map((el: { insert: { formula: string } }) => {
            if (typeof el.insert === 'object') {
                text += `\\(${el.insert.formula}\\)`
            } else if (typeof el.insert === 'string') {
                text += el.insert
            }
        })
        return text
    }

    const insertFormula = (formula: string) => {
        const editor = quillRef.current?.getEditor()
        const range = editor?.getSelection(true)
        if (range) {
            editor?.insertEmbed(range.index, 'formula', `${formula}`)
            editor?.setSelection({ index: range.index + 1, length: 0 })
        }
    }

    const handleChange = (
        content: string,
        delta: any,
        source: any,
        editor: { getContents: () => any }
    ) => {
        if (!disable) {
            onChange(content) // Call the onChange prop
            setText(getText(editor.getContents()), answerid, contentid)
        }
    }

    useEffect(() => {
        if (
            insert &&
            answerid === idanswer &&
            contentid === idcontent &&
            !disable &&
            cortexValue.length > 0
        ) {
            insertFormula(cortexValue[answerid][contentid])
        }
    }, [insert, answerid, idanswer, contentid, idcontent, disable, cortexValue])

    useEffect(() => {
        if (value && value.length > 0) {
            setLocalValue(value)
        }
    }, [value])

    return (
        <ReactQuill
            theme={theme}
            onChange={handleChange}
            value={value}
            modules={modules}
            formats={formats}
            bounds={'.app'}
            placeholder={placeholder}
            ref={quillRef}
            readOnly={disable}
            className={className}
        />
    )
}

export default QuillTextArea
