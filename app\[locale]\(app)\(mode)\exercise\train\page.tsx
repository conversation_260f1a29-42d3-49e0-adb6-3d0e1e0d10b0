import { type Metadata } from 'next'
import { redirect } from 'next/navigation'
import React from 'react'
import { auth } from '@/auth'
import { getMissingKeys } from '@/app/[locale]/actions'
import { Session } from '@/lib/types'
import {
    generateExercise,
    getPartsByChapterId
} from '@/lib/training-mode/actions'
import { getChaptersByDomainAndLevel } from '@/lib/chapters/actions'
import {
    Exo,
    ExoOutput,
    ExoSortsAndFilters,
    getExercise,
    getSortedFilteredExams,
    getSubjectsFromDomainName
} from '@/lib/exams/actions'
import { TrainChat } from './components/training-chat'

export async function generateMetadata(): Promise<Metadata> {
    const session = await auth()

    if (!session?.user) {
        return {}
    }

    // const chat = await getChat(params.id, session.user.id)
    return {
        title: 'Training'
    }
}

export default async function ChatPage() {
    const session = (await auth()) as Session
    const missingKeys = await getMissingKeys()

    if (!session?.user) {
        redirect(`/login`)
    }

    const getChapters = async (domain: string, level: string) => {
        'use server'
        return await getChaptersByDomainAndLevel(domain, level)
    }

    const getExams = async (filters: ExoSortsAndFilters) => {
        'use server'
        return (await getSortedFilteredExams(filters)) as ExoOutput[]
    }

    const getExo = async (id: string) => {
        'use server'
        return (await getExercise(id)) as Exo
    }

    return (
        <TrainChat
            getSubjects={getSubjectsFromDomainName}
            getExams={getExams}
            getChapters={getChapters}
            getParts={getPartsByChapterId}
            session={session}
            initialMessages={[]}
            missingKeys={missingKeys}
            generateExo={generateExercise}
            getExo={getExo}
        />
    )
}
