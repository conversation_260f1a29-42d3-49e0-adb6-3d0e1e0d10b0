import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { getCommentProfEval } from '@/lib/control-mode/actions'
import { cn } from '@/lib/utils/utils'
import { useQuery } from '@tanstack/react-query'
import { Printer } from 'lucide-react'
import moment from 'moment'
import { useLocale, useTranslations } from 'next-intl'
import Image from 'next/image'
import { useParams } from 'next/navigation'
import React, { useRef } from 'react'
import { useReactToPrint } from 'react-to-print'
import { getLangDir } from 'rtl-detect'

type CommentProfEvalProps = {
    isCorrected: boolean
}

const CommentProfEval = ({ isCorrected }: CommentProfEvalProps) => {
    const param = useParams<{ evaluationId: string }>()
    const t = useTranslations('app.mode.controle.prof_review')
    const locale = useLocale()
    const dir = getLangDir(locale)
    const ctrlRef = useRef<HTMLDivElement>(null)
    const { data: cpe } = useQuery({
        queryKey: ['commentProfEval', param.evaluationId],
        queryFn: () => getCommentProfEval(param.evaluationId)
    })

    const printCtrl = useReactToPrint({
        contentRef: ctrlRef,
        bodyClass: 'flex items-center flex-col gap-2'
    })
    const competencies = [
        {
            name: t('name'),
            status: `${cpe?.student.firstName} ${cpe?.student.lastName}`
        },
        {
            name: t('date'),
            status: `${t('du')} ${moment(cpe?.plannedEvaluation?.availableDate).format('DD/MM/YY - HH:mm')} ${t('au')} ${moment(cpe?.plannedEvaluation?.dueDate).format('DD/MM/YY - HH:mm')}`
        },
        { name: t('type'), status: cpe?.control.type },
        { name: t('note'), status: cpe?.globalScore }
    ]

    return (
        <div
            className={`flex w-full items-center flex-col gap-2 ${isCorrected ? 'hidden' : ''}`}
        >
            <div className="w-11/12" ref={ctrlRef}>
                {/* Evaluation summary card */}
                <div className="mt-2  mx-auto">
                    <Card className="border-0 shadow-none">
                        <CardContent className="p-4">
                            <Image
                                width={546}
                                height={63}
                                src="/dino-banniere.png"
                                alt="Header"
                                className="w-full h-[63px] object-cover mb-6"
                            />
                            <div className="flex flex-col gap-4">
                                <h2 className="font-bold text-[12.5px] text-center mb-2">
                                    {t('title')}
                                    {cpe?.control.name}
                                </h2>
                                <div className="border-x border-t border-black">
                                    <div className="grid grid-cols-3">
                                        {competencies.map(
                                            (competency, index) => (
                                                <React.Fragment key={index}>
                                                    <div
                                                        className={cn(
                                                            'font-normal  text-[11px] p-2 border-b border-r border-black',
                                                            dir === 'rtl' &&
                                                                'border-l border-r-0'
                                                        )}
                                                    >
                                                        {competency.name}
                                                    </div>
                                                    <div
                                                        className={`font-medium col-span-2 text-[11px] p-2 border-b border-black`}
                                                    >
                                                        {competency.status}
                                                    </div>
                                                </React.Fragment>
                                            )
                                        )}
                                    </div>
                                </div>
                                <div className="border border-black">
                                    <div className="font-bold text-[11px] p-2 border-b  border-black">
                                        Commentaire :
                                    </div>
                                    <div className="p-2 font-normal text-[11px]">
                                        {cpe?.globalFeedback}
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
            <div className="flex justify-end w-full">
                <Button
                    variant="ghost"
                    className="size-5 p-0 hover:bg-background mr-10"
                    onClick={() => printCtrl()}
                >
                    <Printer />
                </Button>
            </div>
        </div>
    )
}

export default CommentProfEval
