'use client'
import { Link, usePathname } from '@/i18n/routing'
import { selectUsePDFViewerStore } from '@/lib/stores/pdf-viewer-store/pdf-viewer-store'
import { ChevronRight, NotebookTabs, Settings } from 'lucide-react'
import { useCookies } from 'next-client-cookies'
import { useLocale, useTranslations } from 'next-intl'
import React, { useState } from 'react'
import { getLangDir } from 'rtl-detect'
import { selectUseExamStore } from '../(mode)/store/exams-store'
import {
    getFeaturesFlagsByName,
    getFeaturesFlagsByNames
} from '@/lib/features/actions'
import { FeatureFlagName } from '@prisma/client'
import { useQuery } from '@tanstack/react-query'

const StudentSidebar = () => {
    const t = useTranslations('app.sidebar')
    const pathname = usePathname()
    const [active, setActive] = useState(false)
    const locale = useLocale()
    const dir = getLangDir(locale)
    const setIsPDFViewerOpen = selectUsePDFViewerStore.use.setIsPDFViewerOpen()
    const setExercise = selectUseExamStore.use.setExercise()

    const { data: featureFlags } = useQuery({
        queryKey: [
            FeatureFlagName.STUDENT_CLASSES_VIEW,
            FeatureFlagName.STUDENT_CHAT_MODE,
            FeatureFlagName.STUDENT_EXERCISE_MODE,
            FeatureFlagName.STUDENT_EVALUATION_MODE,
            FeatureFlagName.STUDENT_EXAM_MODE
        ],
        queryFn: () =>
            getFeaturesFlagsByNames([
                FeatureFlagName.STUDENT_CLASSES_VIEW,
                FeatureFlagName.STUDENT_CHAT_MODE,
                FeatureFlagName.STUDENT_EXERCISE_MODE,
                FeatureFlagName.STUDENT_EVALUATION_MODE,
                FeatureFlagName.STUDENT_EXAM_MODE
            ]),
        enabled: !!pathname,
        refetchOnWindowFocus: false,
        refetchOnReconnect: false,
        refetchInterval: false,
        refetchIntervalInBackground: false,
        refetchOnMount: false,
        staleTime: Infinity
    })

    const isChatModeEnabled =
        Array.isArray(featureFlags) &&
        featureFlags
            .filter(
                flag =>
                    flag.featureName !== FeatureFlagName.STUDENT_CLASSES_VIEW
            )
            .some(flag => flag.isEnabled)

    const isClassesViewEnabled =
        Array.isArray(featureFlags) &&
        featureFlags.find(
            flag => flag.featureName === FeatureFlagName.STUDENT_CLASSES_VIEW
        )?.isEnabled

    const workLinks: { name: string; path: string; activeIf: string[] }[] = []
    if (isChatModeEnabled) {
        workLinks.push({
            name: t('dino'),
            path: '/',
            activeIf: ['/']
        })
    }
    if (isClassesViewEnabled) {
        workLinks.push({
            name: t('my_class'),
            path: '/my-courses',
            activeIf: ['/my-courses']
        })
    }
    const studentLinks = [
        {
            icon: <NotebookTabs />,
            label: t('work'),
            links: workLinks
        }
        // {
        //     icon:<UserRound />,
        //     label: 'Mon compte',
        //     links: [
        //         {
        //             name: 'Mes informations',
        //             path: '/l',
        //             activeIf: ['/l']
        //         },
        //         {
        //             name: 'Mon profil',
        //             path: '/g',
        //             activeIf: ['/g']
        //         },
        //         {
        //             name: 'Mon parcours',
        //             path: '/w',
        //             activeIf: ['/w']
        //         },
        //         {
        //             name: 'Mon calendrier',
        //             path: '/a',
        //             activeIf: ['/a']
        //         },
        //     ]
        // }
    ]
    const cookies = useCookies()
    const onResetFeature = () => {
        setExercise(null)
        setIsPDFViewerOpen(false)
        cookies.set('feature', 'Chat')
    }
    return (
        <div
            className={`relative z-50 ${active ? 'w-72 px-10 py-2' : 'w-0'} h-full transition-all duration-300 ease-in-out bg-dinoBotBlue`}
        >
            <div className=" flex flex-col justify-between h-full overflow-hidden ">
                <div></div>
                <div className="w-96 flex flex-col gap-8 justify-between">
                    {studentLinks.map((item, index) => (
                        <div
                            key={index}
                            className=" text-white flex flex-col gap-2"
                        >
                            <div className="flex gap-2 text-xl font-bold text-nowrap">
                                <div>{item.icon}</div>
                                <p>{item.label}</p>
                            </div>
                            <div className="flex flex-col text-lg font-light gap-1">
                                {item.links.map(link => (
                                    <Link
                                        className={`hover:underline transition-all duration-200 text-nowrap pl-2 ${link?.activeIf.includes(pathname) ? 'font-semibold underline' : ''}`}
                                        href={link?.path || ''}
                                        key={link?.path}
                                        onClick={onResetFeature}
                                    >
                                        {link?.name}
                                    </Link>
                                ))}
                            </div>
                        </div>
                    ))}
                </div>
                <div>
                    <div className="size-10 rounded-full bg-dinoBotWhite text-dinoBotVividOrange flex justify-center items-center hover:text-dinoBotVividOrange/80 transition-all duration-300 cursor-pointer">
                        <Settings className="size-8" />
                    </div>
                </div>
            </div>

            <button
                className={`absolute ${dir === 'ltr' ? '-right-8 rounded-r-3xl' : '-left-8 rounded-l-3xl'} top-1/2 w-8 h-32 -translate-y-1/2 bg-dinoBotBlue flex items-center justify-center text-white pr-1 after:bg-transparent after:absolute after:size-4 after:z-auto after:-top-4 ${dir === 'ltr' ? 'after:left-0 after:rounded-bl-3xl after:shadow-[-5px_5px_0px_1px_#1861B0]' : 'after:right-0 after:rounded-br-3xl after:shadow-[5px_5px_0px_1px_#1861B0]'} before:bg-transparent before:absolute before:size-4 before:z-auto before:-bottom-4 ${dir === 'ltr' ? 'before:left-0 before:rounded-tl-3xl before:shadow-[-5px_-5px_0px_1px_#1861B0]' : 'before:right-0 before:rounded-tr-3xl before:shadow-[5px_-5px_0px_1px_#1861B0]'}`}
                onClick={() => setActive(a => !a)}
            >
                <ChevronRight
                    className={`${active ? (dir === 'ltr' ? 'rotate-180' : '') : dir === 'ltr' ? 'rotate-0' : 'rotate-180'} transition duration-300 ease-in-out`}
                />
            </button>
        </div>
    )
}

export default StudentSidebar
