import { NextRequest, NextResponse } from 'next/server'
import { sendAccountHasBeenDeletedEmail } from '@/lib/mail-utils'
import { logger } from '@/logger/logger'
import { auth } from '@/auth' // Assuming you might want to protect this endpoint

interface UserAutoDeletionRequestBody {
    emails: string[]
    // To personalize emails, you could pass an array of objects:
    // users: Array<{ email: string; name?: string }>;
}

export async function POST(request: NextRequest) {
    try {
        // Optional: Protect the endpoint
        const bearer = request.headers.get('Authorization')
        const token = bearer?.replace('Bearer ', '')
        if (token !== process.env.PORTAL_API_KEY) {
            return new Response('Unauthorized', { status: 401 })
        }
        const body: UserAutoDeletionRequestBody = await request.json()

        if (
            !body.emails ||
            !Array.isArray(body.emails) ||
            body.emails.length === 0
        ) {
            return NextResponse.json(
                {
                    message:
                        'Bad Request: emails array is required and cannot be empty.'
                },
                { status: 400 }
            )
        }

        logger.info(
            `Received request to send account deletion emails to: ${body.emails.join(', ')}`
        )

        // Send emails without awaiting all of them to complete to avoid long request times
        // Log errors for individual email sending failures
        body.emails.forEach(async email => {
            try {
                // If you have names and want to pass them:
                // await sendAccountHasBeenDeletedEmail(email, userName);
                await sendAccountHasBeenDeletedEmail(email)
                logger.info(
                    `Account deletion confirmation email initiated for: ${email}`
                )
            } catch (error) {
                logger.error(
                    `Failed to send account deletion email to ${email}:` + error
                )
            }
        })

        return NextResponse.json(
            {
                message:
                    'Account deletion email processing initiated for all users.'
            },
            { status: 202 } // 202 Accepted: request accepted for processing
        )
    } catch (error: any) {
        logger.error('Error in user-auto-deletion API route:', error)
        if (error instanceof SyntaxError) {
            // JSON parsing error
            return NextResponse.json(
                { message: 'Bad Request: Invalid JSON.' },
                { status: 400 }
            )
        }
        return NextResponse.json(
            { message: 'Internal Server Error' },
            { status: 500 }
        )
    }
}
