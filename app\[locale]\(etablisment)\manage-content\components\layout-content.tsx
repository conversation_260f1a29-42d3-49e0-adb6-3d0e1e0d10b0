'use client'
import { useTranslations } from 'next-intl'
import React, { ReactNode } from 'react'
import TabHeader from './tab-header'

interface ManageContentLayoutProps {
    children: ReactNode
    module: ReactNode
}
const LayoutContent = ({ children, module }: ManageContentLayoutProps) => {
    const t = useTranslations('establishment.manage_content')

    return (
        <div className="size-full overflow-hidden flex flex-col py-4 gap-4">
            <div className="w-full flex flex-col gap-4 px-10">
                <div className="flex gap-3 items-center">
                    <div className="text-4xl font-bold text-dinoBotGray/90">
                        {t('title')}
                    </div>
                </div>
            </div>
            <div className="w-full grow overflow-y-auto app-scroller pt-4 pr-4 flex">
                <div className="w-fit">
                    <TabHeader />
                    {module}
                </div>
                <div className="flex-1 border-y-2 border-r-2 rounded-r-sm border-dinoBotGray h-[99.7%]">
                    {children}
                </div>
            </div>
        </div>
    )
}

export default LayoutContent
