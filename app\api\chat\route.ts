import { checkChatExistence } from '@/app/[locale]/actions'
import { auth } from '@/auth'
import { getUserPrompts } from '@/lib/admin/actions'
import { buildConditionalPrompt } from '@/lib/ai/prompts/chat/chat-prompts'
import { ChatPrompts } from '@/lib/ai/prompts/chat/types'
import {
    PromptFeatureType,
    SystemPromptService
} from '@/lib/ai/prompts/system-prompt-service'
import { Chat, Session } from '@/lib/types'
import { openai } from '@ai-sdk/openai'
import { streamText } from 'ai'
import { getTranslations } from 'next-intl/server'
import { hasLocale } from 'next-intl'
import { cookies } from 'next/headers'
import { routing } from '@/i18n/routing'
import {
    getRandomErrorMessage,
    saveMessages,
    sendFileToMathpixOcr,
    sendFileToMistralOcr
} from './actions'
import { logger } from '@/logger/logger'
import { fetchProviderAndModelByTask } from '@/lib/ai/llm/server'
import { registry } from '@/lib/ai/config-ai'
import { LlmModel } from '@/prisma/generated/zod/modelSchema/LlmModelSchema'
import { getCurrentLLM } from '@/lib/ai/llm/generator'
// Allow streaming responses up to 30 seconds
export const maxDuration = 30
const documentOcr = process.env.DOCUMENT_OCR ?? 'mistral'

const ELEVE_EXERCICE = 'Élève / Exercice' as const
const ELEVE_CHAT = 'Élève / Chat' as const
const ELEVE_EXAM = 'Élève / Examen' as const

export async function POST(req: Request) {
    // Extract the `messages` from the body of the request
    const { messages, id, imageFile, fileExtension, exercise, currentPDF } =
        await req.json()
    const userMessageCreatedAt = new Date()

    // Extract locale from request URL or use 'fr' as fallback
    const { searchParams } = new URL(req.url)
    const requestedLocale = searchParams.get('locale')
    const locale = hasLocale(routing.locales, requestedLocale)
        ? requestedLocale
        : 'fr'

    if (!requestedLocale) {
        logger.warn(
            `[POST] No locale specified in URL, using default: ${locale}`
        )
    }

    const chatPrompt = (await SystemPromptService.getSystemPrompt(
        PromptFeatureType.CHAT
    )) as ChatPrompts
    const session = await auth()
    const userPrompts = session?.user?.isAdmin
        ? await getUserPrompts(session.user.id!)
        : null
    const path = `/chat/${id}`
    const existingChat = await checkChatExistence(path)

    const useCookies = await cookies()
    const feature = useCookies.get('feature')?.value
    const topic = useCookies.get('topic')?.value
    const mode = useCookies.get('mode')?.value
    const currentExerciseId = useCookies.get('exerciseId')?.value
    const deleteCookies = () => useCookies.delete('exerciseId')

    const t = await getTranslations({ locale, namespace: 'lib.prompts' })

    logger.debug(`[POST] Using language: ${t('lang')}`)

    // Traitement OCR Mathpix si un fichier est présent
    let mathpixOcrText = ''
    let fileBase64 = ''
    const enhancedMessages = [...messages]

    if (imageFile && fileExtension) {
        try {
            logger.info('Processing file with Mathpix OCR')
            const mathpixResult =
                documentOcr === 'mistral'
                    ? await sendFileToMistralOcr(imageFile, fileExtension)
                    : await sendFileToMathpixOcr(imageFile, fileExtension)
            fileBase64 = mathpixResult.fileBase64
            mathpixOcrText = mathpixResult.text

            console.log('Mathpix OCR text: ' + mathpixOcrText)

            // Si OCR a réussi, ajouter le texte extrait au dernier message utilisateur
            if (mathpixOcrText && enhancedMessages.length > 0) {
                const lastUserMessageIndex = enhancedMessages.length - 1
                const lastUserMessage = enhancedMessages[lastUserMessageIndex]

                if (lastUserMessage.role === 'user') {
                    // Ajouter le texte OCR au message utilisateur
                    enhancedMessages[lastUserMessageIndex] = {
                        ...lastUserMessage,
                        content: `${lastUserMessage.content}\n\nContenu extrait du fichier:\n${mathpixOcrText}`,
                        parts: undefined
                    }
                }
            }
        } catch (error) {
            logger.error('Error processing file with Mathpix:', error)
        }
    }

    const providerAndModel = await fetchProviderAndModelByTask(
        feature == 'Chat'
            ? ELEVE_CHAT
            : feature === 'Exo'
              ? ELEVE_EXERCICE
              : ELEVE_EXAM
    )
    // --- Use the generic streamer ---
    const currentLLM = getCurrentLLM(mode || 'GPT 4-o')
    let actualModel = openai(currentLLM)
    const { model, profile } = providerAndModel
    const { name } = profile as LlmModel
    if (model && name) {
        actualModel = (await registry()).languageModel(`${name}:${model}`)
    }
    const systemPromptContent =
        `\n Réponds en utilisant la langue de l'utilisateur: ${t('lang')} \n` +
        (chatPrompt.mainPrompt.prompt ?? userPrompts?.mainPrompt) +
        (await buildConditionalPrompt(
            feature,
            undefined,
            topic,
            currentPDF?.assignment
        )) +
        (exercise
            ? `Voilà le contenu de l'exercice en question au format json: \n\n ${JSON.stringify(exercise)} \n\n`
            : '')

    // Call the language model with enhanced messages
    const result = streamText({
        model: actualModel,
        messages: enhancedMessages,
        system: systemPromptContent,
        onFinish: saveMessages({
            messages,
            userMessageCreatedAt,
            id,
            currentExerciseId,
            deleteCookies,
            existingChat: existingChat as Chat,
            feature,
            session: session as Session,
            topic,
            imageFile,
            fileExtension,
            fileBase64
        }),
        experimental_telemetry: {
            isEnabled: true,
            functionId: `[${feature}]streamText`,
            metadata: {
                tags: [feature],
                userId: session?.user?.email ?? 'unknown'
            }
        }
    })
    const randomErrorText = await getRandomErrorMessage()
    return result.toDataStreamResponse({
        getErrorMessage: error => {
            logger.error(`Stream error: ${JSON.stringify(error)} /`)
            return randomErrorText
        }
    })
}
