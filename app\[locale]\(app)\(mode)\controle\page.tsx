import Timer from './components/Timer/timer'
import {
    ControlModeGeneratorInput,
    generateControle,
    generateFeedback
} from '@/lib/control-mode/generator/actions'
import { ControlFeedbackOutput } from '@/lib/control-mode/types/control/types'
import ControlPreview from './components/control-preview'
import AnswerControl from './components/answer-control'
import React from 'react'

const Controle = async () => {
    const getControle = async ({
        chapterId,
        domain
    }: ControlModeGeneratorInput) => {
        'use server'
        return await generateControle({ chapterId, domain })
    }

    const generateControlFeedback = async (controle: ControlFeedbackOutput) => {
        'use server'
        return await generateFeedback(controle)
    }
    return (
        <div className="flex size-full flex-col xl:flex-row relative grow">
            <div className="xl:absolute fixed xl:top-5 xl:bottom-auto bottom-2 left-1/2 -translate-x-1/2  z-20">
                <Timer />
            </div>
            <div className=" xl:w-1/2 w-full border-l-gray-700 border-2 relative">
                <ControlPreview getControle={getControle} />
            </div>
            <div className=" xl:w-1/2 w-full border-r-gray-700 border-2">
                <AnswerControl
                    generateControlFeedback={generateControlFeedback}
                />
            </div>
        </div>
    )
}

export default Controle
