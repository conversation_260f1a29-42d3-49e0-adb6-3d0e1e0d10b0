import Stripe from 'stripe'
import { stripe } from '@/lib/stripe/config'
import {
    upsertProductRecord,
    upsertPriceRecord,
    manageSubscriptionStatusChange,
    deleteProductRecord,
    deletePriceRecord
} from '@/lib/stripe/actions'
import { logger } from '@/logger/logger'

const relevantEvents = new Set([
    'product.created',
    'product.updated',
    'product.deleted',
    'price.created',
    'price.updated',
    'price.deleted',
    'checkout.session.completed',
    'customer.subscription.created',
    'customer.subscription.updated',
    'customer.subscription.deleted'
])

// This is the webhook handler function for Stripe
export async function POST(req: Request) {
    const body = await req.text()
    logger.info(`Webhook event recieved ${JSON.stringify(body)}`)

    // This should fend off any unauthorized bozos who try to hit this webhook xdd
    const sig = req.headers.get('stripe-signature') as string

    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET
    let event: Stripe.Event

    try {
        if (!sig || !webhookSecret)
            return new Response('Webhook secret not found.', { status: 400 })
        event = stripe.webhooks.constructEvent(body, sig, webhookSecret)
        logger.info(`🔔  Webhook received: ${JSON.stringify(event.type)}`)
    } catch (err: any) {
        logger.info(`❌ Error message: ${JSON.stringify(err.message)}`)
        return new Response(`Webhook Error: ${err.message}`, { status: 400 })
    }

    if (relevantEvents.has(event.type)) {
        try {
            switch (event.type) {
                case 'product.created':
                case 'product.updated':
                    await upsertProductRecord(
                        event.data.object as Stripe.Product
                    )
                    const product = event.data.object as Stripe.Product
                    logger.info(`Product: ${JSON.stringify(product)}`)
                    break
                case 'price.created':
                case 'price.updated':
                    const price = event.data.object as Stripe.Price
                    logger.info(`Price: ${JSON.stringify(price)}`)
                    await upsertPriceRecord(event.data.object as Stripe.Price)
                    break
                case 'price.deleted':
                    const deletedPrice = event.data.object as Stripe.Price
                    logger.info(`Price: ${JSON.stringify(deletedPrice)}`)
                    await deletePriceRecord(event.data.object as Stripe.Price)
                    break
                case 'product.deleted':
                    const deletedProduct = event.data.object as Stripe.Product
                    logger.info(`Product: ${JSON.stringify(deletedProduct)}`)
                    await deleteProductRecord(
                        event.data.object as Stripe.Product
                    )
                    break
                case 'customer.subscription.created':
                case 'customer.subscription.updated':
                case 'customer.subscription.deleted':
                    const subscription = event.data
                        .object as Stripe.Subscription
                    logger.info(subscription)
                    await manageSubscriptionStatusChange(
                        subscription.id,
                        subscription.customer as string
                    )
                    break
                case 'checkout.session.completed':
                    const checkoutSession = event.data
                        .object as Stripe.Checkout.Session
                    if (checkoutSession.mode === 'subscription') {
                        const subscriptionId = checkoutSession.subscription
                        await manageSubscriptionStatusChange(
                            subscriptionId as string,
                            checkoutSession.customer as string
                        )
                    }
                    break
                default:
                    throw new Error('Unhandled relevant event!')
            }
        } catch (error) {
            logger.error(
                `An error has occured during stripe webhook handling: \n ${error}`
            )
            return new Response(
                'Webhook handler failed. View your Next.js function logs.',
                {
                    status: 400
                }
            )
        }
    } else {
        logger.info(`Unsupported event type: ${event.type}`)
        return new Response(`Unsupported event type: ${event.type}`, {
            status: 400
        })
    }
    logger.info('Webhook event handled successfully')
    return new Response(JSON.stringify({ received: true }))
}
