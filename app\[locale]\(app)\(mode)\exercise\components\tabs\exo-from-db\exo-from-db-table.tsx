import { Question } from '@/lib/training-mode/types'
import React, { useEffect, useState } from 'react'

interface ExoFromDbTableProps {
    questions: Question[]
    onValueChange: (value: string) => void
}

function ExoFromDbTable({ questions, onValueChange }: ExoFromDbTableProps) {
    const [selectedItem, setSelectedItem] = useState<string | null>(null)

    useEffect(() => {
        if (selectedItem) onValueChange(selectedItem)
    }, [selectedItem])

    return (
        <div className="custom-scroller max-h-40 pr-6 overflow-y-scroll">
            <div className="flex flex-col gap-1 ">
                {questions.map(q => (
                    <div
                        className={`w-full py-1 px-3 bg-dinoBotLightGray/80 cursor-pointer hover:bg-dinoBotVibrantBlue/10 border text-sm rounded-md transition-all duration-200 ${selectedItem === q.id ? 'bg-dinoBotVibrantBlue text-white hover:bg-dinoBotVibrantBlue/80' : ''}`}
                        key={q.id}
                        onClick={() => setSelectedItem(q.id)}
                    >
                        {q.knowledge}
                    </div>
                ))}
            </div>
        </div>
    )
}

export default ExoFromDbTable
