'use client'
import React, { useCallback, useRef } from 'react'
import { Input } from '@/components/ui/input'
import { Search } from 'lucide-react'
import { selectSkillsStore } from '../store/skills.store'
import { useQueryClient } from '@tanstack/react-query'
import { selectUseContentStore } from '../../store/content.store'
import { useTranslations } from 'next-intl'

const SkillFilter = () => {
    const t = useTranslations('establishment.manage_content.skills')
    const searchFilter = selectSkillsStore.use.searchFilter()
    const setSearchFilter = selectSkillsStore.use.setSearchFilter()
    const queryClient = useQueryClient()
    const domainId = selectUseContentStore.use.domainId()
    const levelId = selectUseContentStore.use.levelId()
    const timeoutRef = useRef<NodeJS.Timeout | null>(null)

    const debouncedInvalidate = useCallback(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
        }
        timeoutRef.current = setTimeout(() => {
            queryClient.invalidateQueries({
                queryKey: ['chapters-with-skills', domainId, levelId]
            })
        }, 500)
    }, [queryClient, domainId, levelId])

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value
        setSearchFilter(value)

        debouncedInvalidate()
    }

    return (
        <div className="relative w-80">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
                type="text"
                placeholder={t('search_placeholder')}
                value={searchFilter}
                onChange={handleSearchChange}
                className="pl-10 pr-4 py-2"
            />
        </div>
    )
}

export default SkillFilter
