import { NextResponse, NextRequest } from 'next/server'
import { transcribeAudio } from '@/lib/voice/actions'
import { logger } from '@/logger/logger'

export async function POST(req: NextRequest) {
    logger.info('REST request to receive audio data for transcription')

    const body = await req.json()

    const base64Audio = body.audio

    try {
        const text = await transcribeAudio(base64Audio)

        return NextResponse.json({ transcription: text })
    } catch {
        logger.error('Error processing audio')
        return NextResponse.error()
    }
}
