'use client'
import Image from 'next/image'
import useCtrlModeStore from '@/app/[locale]/(app)/(mode)/controle/store/controle-store'
import { useCookies } from 'next-client-cookies'
import CtrlGenerator from './ctrl-generator'
import { FileText, X } from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { Chapter, Part } from '@/lib/training-mode/types'
import Loading from '@/app/[locale]/loading'
import { Button } from '@/components/ui/button'
import MiniPDFReader from '@/components/pdf-reader/pdf-reader-mini'

type CtrlModeLayoutProps = {
    getChapters: (domain: string, level: string) => Promise<Chapter[]>
    getParts: (chapterId: string) => Promise<Part[]>
}
const CtrlModeLayout = ({ getChapters, getParts }: CtrlModeLayoutProps) => {
    const { reset, subject, setSubject } = useCtrlModeStore()
    const [pdf] = useState<any>(null)
    const [pdfIsOpen, setPDFIsOpen] = useState<boolean>(true)
    const LoginMode =
        (process.env.NEXT_PUBLIC_LOGIN_TYPE ?? process.env.LOGIN_TYPE) ==
        'dinobot'
            ? true
            : false
    const cookies = useCookies()
    useEffect(() => {
        reset()
        setSubject(cookies.get('topic')!)
    }, [])
    return (
        <div className=" relative w-full min-h-full mb-20 flex justify-center ">
            {!subject ? (
                <Loading />
            ) : (
                <div className="z-20 mt-4 sm:mt-10">
                    <CtrlGenerator
                        getChapters={getChapters}
                        getParts={getParts}
                    />
                </div>
            )}
            {LoginMode && (
                <div className="fixed bottom-0 left-0 size-80 sm:size-96">
                    <Image src="/dinobot.svg" fill alt="dinobot" />
                </div>
            )}
            <CtrlPdfViewer
                show={pdfIsOpen}
                pdf={pdf}
                onClose={() => setPDFIsOpen(false)}
                onOpen={() => setPDFIsOpen(true)}
            />
        </div>
    )
}

export default CtrlModeLayout

interface PdfViewerProps {
    show: boolean
    pdf: any
    onClose: () => void
    onOpen: () => void
}
function CtrlPdfViewer({ show, pdf, onClose, onOpen }: PdfViewerProps) {
    return pdf && show ? (
        <div className="size-full  sm:w-[500px] sm:h-[600px] p-1 fixed bottom-1/2 translate-y-1/2 2xl:right-10 z-50 bg-white custom-scroller border border-dinoBotGray overflow-hidden">
            <div className=" px-2 py-1 w-full flex justify-end items-center">
                <Button
                    variant="ghost"
                    className="p-0 size-7 rounded-full hover:animate-wiggle hover:text-dinoBotRed text-dinoBotRed "
                    onClick={onClose}
                >
                    <X className="size-5" />
                </Button>
            </div>
            <div className="size-full overflow-y-auto overflow-x-hidden  custom-scroller">
                <div className=" flex items-center shadow-md flex-col justify-start">
                    <div className="overflow-auto">
                        <MiniPDFReader file={pdf} sideViewer={true} />
                    </div>
                </div>
            </div>
        </div>
    ) : pdf ? (
        <div
            className="size-12 z-50 fixed  bottom-1/2 translate-y-1/2 right-10 rounded-full bg-dinoBotLightBlue border border-dinoBotVibrantBlue flex justify-center items-center text-dinoBotVibrantBlue cursor-pointer"
            onClick={onOpen}
        >
            <div>
                <FileText />
            </div>
        </div>
    ) : null
}
