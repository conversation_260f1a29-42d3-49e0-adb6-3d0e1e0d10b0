import { NextRequest, NextResponse } from 'next/server'
import { join } from 'path'
import { mkdir, stat, writeFile } from 'fs/promises'
import mime from 'mime'

export async function POST(_req_: NextRequest) {
    const formData = await _req_.formData()
    const image = formData.get('image') as File | null

    if (!image) {
        return NextResponse.json(
            { error: 'No image provided.' },
            { status: 400 }
        )
    }

    const buffer = Buffer.from(await image.arrayBuffer())
    //const base64Image = buffer.toString('base64');

    const extension = mime.getExtension(image.type)
    const id = crypto.randomUUID()
    //const filename = `${id}.${extension}`;
    const originalName = image.name
    const uploadDir = join(process.cwd(), 'public', 'uploads')

    try {
        await stat(uploadDir)
    } catch (_e_: any) {
        if (_e_.code === 'ENOENT') {
            await mkdir(uploadDir, { recursive: true })
        } else {
            return NextResponse.json(
                { error: 'Something went wrong.' },
                { status: 500 }
            )
        }
    }

    try {
        const filePath = join(uploadDir, `${originalName}`)
        await writeFile(filePath, buffer)
        return NextResponse.json({ id, originalName, extension })
    } catch {
        return NextResponse.json(
            { error: 'Something went wrong.' },
            { status: 500 }
        )
    }
}
