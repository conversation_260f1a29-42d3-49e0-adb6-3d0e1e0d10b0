import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import React, { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'

import InfoTooltip from '@/components/ui/info-tooltip'
import { Chapter, Part } from '@/lib/training-mode/types'
import { Input } from '@/components/ui/input'
import { Slider } from '@/components/ui/slider'
import { selectUseExoModeStore } from '@/lib/stores/exercise-mode-store/exercise-store'
import { toast } from 'sonner'
import ErrorTooltip from '@/components/ui/error-tooltip'
import { useLocale, useTranslations } from 'next-intl'
import { MultiSelect } from '@/components/ui/multi-select'
import { getLangProps } from '@/lib/utils/string.utils'
import { Domain } from '@prisma/client'
import useAccountStore from '@/lib/stores/account-store/store'
import { useCookies } from 'next-client-cookies'

interface ExoFromDbProps {
    domains: Domain[]
    chapters: Chapter[]
    getChapters: (domain: string, level: string) => Promise<Chapter[]>
    getParts: (chapterId: string) => Promise<Part[]>
    onGenerate?: () => Promise<void>
}

function ExoFromDbMin({
    domains,
    chapters,
    getChapters,
    getParts,
    onGenerate
}: ExoFromDbProps) {
    const formData = selectUseExoModeStore.use.exoInfoFromDb()
    const handleFormChange = selectUseExoModeStore.use.updateExoInfoFromDb()
    const [parts, setParts] = useState<Part[]>([])
    const cookies = useCookies()
    const [selectedChapter, setSelectedChapter] = useState<string>(
        formData.chapterId
    )
    const tMultiSelect = useTranslations('components.multiselect')
    const [showError, setShowError] = useState<boolean>(false)
    const setMode = selectUseExoModeStore.use.setMode()
    const t = useTranslations('app.mode.exo.mini-tabs.db')
    const t2 = useTranslations('app.mode.exo.tab.db')
    const locale = useLocale()
    const { user } = useAccountStore()
    const levelIdFromCookie = cookies.get('levelId')

    useEffect(() => {
        setMode('FROM_DB')
    }, [])

    // Set default domain if formData.domainName is empty and domains are available
    useEffect(() => {
        if (!formData.domainName && domains.length > 0) {
            handleFormChange('domainName', domains[0].name)
        }
    }, [domains, formData.domainName])

    useEffect(() => {
        if (
            formData.domainName &&
            formData.domainName.length > 0 &&
            levelIdFromCookie
        ) {
            ;(async () => {
                const chaps = await getChapters(
                    formData.domainName,
                    levelIdFromCookie!
                )
                // setChapters(chaps) // chapters are passed as props
                // Set default chapter if formData.chapterId is empty and chapters are available
                if (!formData.chapterId && chaps.length > 0) {
                    setSelectedChapter(chaps[0].id)
                }
            })()
        } else {
            // setChapters([]) // chapters are passed as props
        }
    }, [formData.domainName, levelIdFromCookie, chapters]) // Added chapters to dependency array

    useEffect(() => {
        if (selectedChapter) {
            handleFormChange('chapterId', selectedChapter)
            ;(async () => {
                const parts = await getParts(selectedChapter)
                setParts(parts)
            })()
        }
    }, [selectedChapter])

    const normalizeExerciseNumber = (value: number): number => {
        if (value > 10) return 10
        if (value < 1) return 1
        return value
    }

    const showLimitInfo = (value: number) => {
        if (value > 10) {
            toast.info(t('n10'), { duration: 2500 })
        } else if (value < 1) {
            toast.info(t('n1'), { duration: 2500 })
        }
    }

    const handleNumberChange = (
        field: 'qstNbr' | 'exoNbr',
        value: number,
        defaultValue: number
    ) => {
        try {
            const normalizedValue = normalizeExerciseNumber(value)
            showLimitInfo(value)
            handleFormChange(field, normalizedValue)
        } catch (error) {
            console.log(error)
            handleFormChange(field, defaultValue)
        }
    }

    const handleQuestionNumberChange = (value: number) => {
        handleNumberChange('qstNbr', value, 5)
    }

    const handleExerciseNumberChange = (value: number) => {
        handleNumberChange('exoNbr', value, 3)
    }

    useEffect(() => {
        if (formData.qstNbr) handleQuestionNumberChange(formData.qstNbr)
    }, [formData.qstNbr])

    useEffect(() => {
        if (formData.exoNbr) handleExerciseNumberChange(formData.exoNbr)
    }, [formData.exoNbr])

    const submit = async () => {
        if (formData.chapterId && formData.partIds.length > 0) {
            if (formData.qstNbr && formData.exoNbr) {
                setShowError(false)
                setMode('FROM_DB')
                if (onGenerate) await onGenerate()
            } else {
                toast.error(t('n1&10'))
            }
        } else {
            setShowError(true)
            toast.info(t('error'))
        }
    }

    return (
        <div className="flex flex-col gap-4">
            <div className="w-full flex flex-col md:flex-row gap-2">
                <div className="w-full flex flex-col lg:flex-row gap-2">
                    <div className="w-1/2">
                        <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                            {t2('subject.name')}{' '}
                        </div>
                        <Select
                            value={formData.domainName}
                            onValueChange={value => {
                                handleFormChange('domainName', value)
                                handleFormChange('chapterId', '') // Reset chapterId when domain changes
                                handleFormChange('partIds', '')
                                setSelectedChapter('') // Reset selectedChapter state
                                const selectedDomain = domains.find(
                                    domain => domain.name === value
                                )
                                if (selectedDomain) {
                                    cookies.set(
                                        'topicId',
                                        selectedDomain.id.toString()
                                    )
                                    cookies.set('topic', selectedDomain.name)
                                }
                            }}
                        >
                            <SelectTrigger className="max-w-full">
                                <SelectValue
                                    placeholder={t2('subject.placeholder')}
                                />
                            </SelectTrigger>
                            <SelectContent
                                className={`${domains.length > 5 ? 'h-48' : 'h-fit'}`}
                            >
                                <SelectGroup>
                                    {domains.map(domain => (
                                        <SelectItem
                                            key={domain.id}
                                            value={domain.name}
                                        >
                                            {getLangProps({
                                                obj: domain,
                                                base: 'name',
                                                lang: locale
                                            })}
                                        </SelectItem>
                                    ))}
                                </SelectGroup>
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="w-1/2">
                        <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                            {t('chap')}{' '}
                            {(formData.chapterId.length <= 0 && !showError) ||
                            formData.chapterId.length > 0 ? (
                                <InfoTooltip message={t('chois')} />
                            ) : (
                                <ErrorTooltip message={t('error-chois')} />
                            )}
                        </div>
                        <Select
                            value={formData.chapterId}
                            onValueChange={value => {
                                handleFormChange('partIds', '')
                                setSelectedChapter(value)
                            }}
                            disabled={
                                chapters.length <= 0 || domains.length <= 0
                            }
                        >
                            <SelectTrigger className=" max-w-full">
                                <SelectValue placeholder={t('chois')} />
                            </SelectTrigger>
                            <SelectContent
                                className={`${chapters.length > 5 ? 'h-48' : 'h-fit'}`}
                            >
                                <SelectGroup>
                                    {chapters.map(chapter => (
                                        <SelectItem
                                            key={chapter.id}
                                            value={chapter.id}
                                        >
                                            {locale === 'ar'
                                                ? chapter.title_ar
                                                : locale === 'en'
                                                  ? chapter.title_en
                                                  : chapter.title}
                                        </SelectItem>
                                    ))}
                                </SelectGroup>
                            </SelectContent>
                        </Select>
                    </div>
                </div>
            </div>
            <div className="w-full flex flex-col md:flex-row gap-2">
                <div className="w-full sm:w-4/5 md:w-1/2">
                    <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('exonumber')}
                    </div>
                    <Input
                        type="number"
                        value={formData.exoNbr}
                        min={1}
                        max={10}
                        className="h-9 rounded-lg"
                        onChange={e =>
                            handleFormChange(
                                'exoNbr',
                                parseInt(e.target.value) || 1
                            )
                        }
                    />
                </div>
                <div className="w-full sm:w-4/5 md:w-1/2">
                    <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('questions-number')}
                    </div>
                    <Input
                        type="number"
                        value={formData.qstNbr}
                        min={1}
                        max={10}
                        className="h-9 rounded-lg"
                        onChange={e =>
                            handleFormChange(
                                'qstNbr',
                                parseInt(e.target.value) || 1
                            )
                        }
                    />
                </div>
            </div>
            <div className="w-full">
                <div className="w-full">
                    <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('part')}{' '}
                        {(formData.partIds?.length <= 0 && !showError) ||
                        formData.partIds?.length > 0 ? (
                            <InfoTooltip message={t('chois-part')} />
                        ) : (
                            <ErrorTooltip message={t('error-chois-part')} />
                        )}
                    </div>
                    <MultiSelect
                        options={parts.map(part => ({
                            label: getLangProps({
                                obj: part,
                                base: 'name',
                                lang: locale
                            }),
                            value: part.id
                        }))}
                        onValueChange={values =>
                            handleFormChange('partIds', values)
                        }
                        defaultValue={formData.partIds}
                        disabled={parts.length <= 0 || chapters.length <= 0}
                        maxCount={2}
                        placeholder={t('chois-part')}
                        badgeclassName="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/80 text-white"
                        translations={{
                            selectAll: tMultiSelect('selectAll'),
                            search: tMultiSelect('search'),
                            clear: tMultiSelect('clear'),
                            noResults: tMultiSelect('noResults'),
                            close: tMultiSelect('close'),
                            more: tMultiSelect('more')
                        }}
                    />
                </div>
            </div>
            <div className="w-full flex flex-col md:flex-row gap-2">
                <div className="w-full sm:w-4/5 md:w-1/2">
                    <div className="flex gap-1 px-[3px] items-center justify-between text-sm font-bold text-dinoBotDarkGray">
                        {t('difficulty')} <span>{formData.difficulty}/3</span>
                    </div>
                    <div className="py-3 px-[3px]">
                        <Slider
                            defaultValue={[formData.difficulty]}
                            min={0}
                            max={3}
                            step={1}
                            className={'w-full '}
                            onValueChange={value =>
                                handleFormChange('difficulty', value[0])
                            }
                        />
                    </div>
                </div>
            </div>

            <div className="w-full flex justify-center items-center mt-6">
                <Button
                    className="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/90 rounded-xl w-36"
                    onClick={submit}
                    disabled={!parts.length}
                >
                    {t('submit')}
                </Button>
            </div>
        </div>
    )
}

export default ExoFromDbMin
