'use client'
import React, { useEffect, useState } from 'react'
import Image from 'next/image'
import useExoModeStore from '@/lib/stores/exercise-mode-store/exercise-store'
import ExoGenerator from './exo-generator'
import { Chapter, Part } from '@/lib/training-mode/types'
import {
    Exo,
    ExoOutput,
    ExoSortsAndFilters,
    ExoSubject
} from '@/lib/exams/actions'
import { useCookies } from 'next-client-cookies'
import { useRouter } from '@/i18n/routing'
import { Button } from '@/components/ui/button'
import { FileText, X } from 'lucide-react'
import MiniPDFReader from '@/components/pdf-reader/pdf-reader-mini'
import { Domain } from '@prisma/client'

interface ExoModeLayoutProps {
    getDomains: (
        level: string
    ) => Promise<Domain[] | { error: string; status: number }>
    getChapters: (domain: string, level: string) => Promise<Chapter[]>
    getParts: (chapterId: string) => Promise<Part[]>
    getExo: (id: string) => Promise<Exo>
    getSubjectsByDomainIdAndLevelId: (
        domainId: number,
        levelId: number | null
    ) => Promise<ExoSubject[]>
}

function ExoModeLayout({
    getDomains,
    getChapters,
    getParts,
    getExo,
    getSubjectsByDomainIdAndLevelId
}: ExoModeLayoutProps) {
    const reset = useExoModeStore(state => state.reset)

    const [pdf, setPDF] = useState<any>(null)
    const [pdfIsOpen, setPDFIsOpen] = useState<boolean>(true)
    const examId = useExoModeStore(state => state.exoInfoFromExam.examId)
    const mode = useExoModeStore(state => state.mode)
    const LoginMode =
        (process.env.NEXT_PUBLIC_LOGIN_TYPE ?? process.env.LOGIN_TYPE) ==
        'dinobot'
            ? true
            : false

    const cookies = useCookies()
    const router = useRouter()

    const openAssignementPDF = async () => {
        const exo = await getExo(examId)
        setPDF({
            name: `${exo.title} - énoncé`,
            type: 'pdf',
            data: `data:application/pdf;base64,${exo.assignmentMedia}`
        })
        setPDFIsOpen(
            Math.max(
                document.documentElement.clientWidth || 0,
                window.innerWidth || 0
            ) > 1895
        )
    }

    useEffect(() => {
        if (examId && mode === 'FROM_EXAM') {
            ;(async () => {
                await openAssignementPDF()
            })()
        } else {
            setPDF(null)
            setPDFIsOpen(false)
        }
    }, [examId, mode])
    useEffect(() => {
        const feat = cookies.get('feature')
        if (feat !== 'Exo') {
            cookies.set('feature', 'Exo')
            router.refresh()
        }
        reset()
    }, [])

    return (
        <div className=" w-full min-h-full mb-20 flex justify-center ">
            {
                <div className="z-20 mt-4 sm:mt-6 2xl:mt-10">
                    <ExoGenerator
                        getDomains={getDomains}
                        getChapters={getChapters}
                        getParts={getParts}
                        getSubjectsByDomainIdAndLevelId={
                            getSubjectsByDomainIdAndLevelId
                        }
                    />
                </div>
            }
            {LoginMode && (
                <div className="fixed bottom-0 left-0 size-80 sm:size-96">
                    <Image src="/dinobot.svg" fill alt="dinobot" />
                </div>
            )}
            {mode === 'FROM_EXAM' && (
                <ExamPdfViewer
                    show={pdfIsOpen}
                    pdf={pdf}
                    onClose={() => setPDFIsOpen(false)}
                    onOpen={() => setPDFIsOpen(true)}
                />
            )}
        </div>
    )
}

export default ExoModeLayout

interface PdfViewerProps {
    show: boolean
    pdf: any
    onClose: () => void
    onOpen: () => void
}

export function ExamPdfViewer({ show, pdf, onClose, onOpen }: PdfViewerProps) {
    return pdf && show ? (
        <div className="size-full  sm:w-[500px] sm:h-[600px] p-1 fixed bottom-1/2 translate-y-1/2 2xl:right-10 z-50 bg-white custom-scroller border border-dinoBotGray overflow-hidden">
            <div className=" px-2 py-1 w-full flex justify-end items-center">
                <Button
                    variant="ghost"
                    className="p-0 size-7 rounded-full hover:animate-wiggle hover:text-dinoBotRed text-dinoBotRed "
                    onClick={onClose}
                >
                    <X className="size-5" />
                </Button>
            </div>
            <div className="size-full overflow-y-auto overflow-x-hidden  custom-scroller">
                <div className=" flex items-center shadow-md flex-col justify-start">
                    <div className="overflow-auto">
                        <MiniPDFReader file={pdf} sideViewer={true} />
                    </div>
                </div>
            </div>
        </div>
    ) : pdf ? (
        <div
            className="size-12 z-50 fixed  bottom-1/2 translate-y-1/2 right-10 rounded-full bg-dinoBotLightBlue border border-dinoBotVibrantBlue flex justify-center items-center text-dinoBotVibrantBlue cursor-pointer"
            onClick={onOpen}
        >
            <div>
                <FileText />
            </div>
        </div>
    ) : null
}
