'use client'
import React, { useEffect, useState } from 'react'
import { EvalInput } from './eval-input'
import {
    selectUseContentStore,
    useContentStore
} from '../../store/content.store'
import { getLevelDomainByLevelIdAndDomainId } from '@/lib/domain-level/actions'
import { getEvaluationPromptsByLevelDomainId } from '@/lib/evaluation-prompts/actions'
import { EvaluationPrompt, EvaluationType, LevelDomain } from '@prisma/client'
import { z } from 'zod'
import EvaluationPromptSchema, {
    EvaluationPromptPartial,
    EvaluationPromptPartialSchema
} from '@/prisma/generated/zod/modelSchema/EvaluationPromptSchema'

type EvalPromptsListProps = {
    dinoUrl?: string
}

function EvalPromptsList({ dinoUrl }: EvalPromptsListProps) {
    const domainId = selectUseContentStore.use.domainId()
    const levelId = selectUseContentStore.use.levelId()
    const setLevelDomainId = selectUseContentStore.use.setLevelDomainId()
    const setDinoUrl = selectUseContentStore.use.setDinoUrl()
    const [evalPrompts, setEvalPrompts] = useState<EvaluationPrompt[]>([])

    useEffect(() => {
        setDinoUrl(dinoUrl!) // save URL in store
    }, [])

    useEffect(() => {
        ;(async () => {
            const levelDomain = (await getLevelDomainByLevelIdAndDomainId(
                levelId,
                domainId!
            )) as LevelDomain
            setLevelDomainId(levelDomain?.id)
            const prompts = (await getEvaluationPromptsByLevelDomainId(
                levelDomain?.id
            )) as z.infer<typeof EvaluationPromptPartialSchema>[]
            setEvalPrompts(prompts as EvaluationPrompt[])
        })()
    }, [domainId, levelId])

    if (!domainId) {
        return (
            <div className="size-full flex items-center justify-center">
                <span>Veuillez choisir un domaine et un niveau</span>
            </div>
        )
    }

    return (
        <ul className="h-full px-8 py-8 flex flex-col gap-0 2xl:gap-16">
            {Object.values(EvaluationType).map((type, index) => {
                return (
                    <li key={index} className="h-1/4 2xl:h-fit">
                        <EvalInput
                            title={type}
                            evalPrompt={
                                (evalPrompts?.find(
                                    e => e.title === type
                                ) as EvaluationPromptPartial) || { prompt: '' }
                            }
                            placeholder="Rédigez un prompt qui donnera un type d’exercice unique..."
                        />
                    </li>
                )
            })}
        </ul>
    )
}

export default EvalPromptsList
