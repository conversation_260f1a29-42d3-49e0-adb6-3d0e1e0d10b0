import React, { useEffect, useReducer, useState, useTransition } from 'react'
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    Di<PERSON>Footer,
    <PERSON><PERSON>Header,
    DialogTitle,
    DialogTrigger
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger
} from '../../../../../../components/ui/accordion'
import { Eye, Loader2, XIcon } from 'lucide-react'
import { selectUseContentStore } from '../../store/content.store'
import { EvaluationType } from '@prisma/client'
import { z } from 'zod'
import questionsTypeSchema from '../../../../../../prisma/generated/zod/inputTypeSchemas/questionsTypeSchema'
import QuestionSchema from '../../../../../../prisma/generated/zod/modelSchema/QuestionSchema'
import FormParamsPromptPreview, {
    formParamsType,
    initFormParams
} from './form-params-prompt-preview'
import { useQuery } from '@tanstack/react-query'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypemathjax from 'rehype-mathjax'
import rehypeRaw from 'rehype-raw'
import { MemoizedReactMarkdown } from '@/components/markdown'

type EvalInputProps = {
    title?: string
    type?: EvaluationType
    evalPrompt: string
}

export const GenerateQuestionsSchema = QuestionSchema.extend({
    questionNbr: z.number().describe('The number of questions to generate'),
    exerciseType: questionsTypeSchema
        .optional()
        .describe('type of the questions to generate reliant or independant')
})

export const llmOutputRecipe = z.array(
    z.object({
        questionContent: z
            .string()
            .describe(
                'The question content, make sure to respect user language !'
            ),
        contentType: z
            .enum(['text', 'html', 'latex'])
            .describe('Le type de formatage du contenu de la question.'),
        solution: z
            .string()
            .describe(
                'Une solution brève à la question, avec un niveau de détails modéré.'
            ),
        questionType: z
            .enum(['qcm', 'text'])
            .describe('Le type de question, soit choix multiple soit texte.')
    })
)

function EvalDialog({ title, type, evalPrompt }: EvalInputProps) {
    const domain = selectUseContentStore.use.domain()
    const level = selectUseContentStore.use.level()
    const dinoUrl = selectUseContentStore.use.dinoUrl()

    const [open, setOpen] = useState<boolean>(false)
    const [preview, setPreview] = useState<
        { question: string; knowledge: string; solution: string }[] | undefined
    >()
    const [formState, dispatch] = useReducer(
        (state: formParamsType, action: Partial<formParamsType>) => ({
            ...state,
            ...action
        }),
        initFormParams
    )

    const [isLoading, startTransition] = useTransition()

    const { data: prompt } = useQuery({
        queryKey: [
            'chapters',
            formState.nbQuestions,
            formState.selectedPart,
            open
        ],
        enabled: !!open,
        queryFn: async () => {
            const body = {
                domain: domain,
                level: level,
                evalPrompt,
                type,
                chapter: formState.selectedChapter ?? undefined,
                part: formState.selectedPart ?? undefined,
                questionNbr: formState.nbQuestions
            }
            const response = await fetch(`${dinoUrl}/fr/api/get-prompt`, {
                cache: 'no-store',
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(body)
            })
            setPreview([])
            const res = await response.json()
            return res
        }
    })

    useEffect(() => {
        dispatch({ selectedPart: '' })
    }, [formState.selectedChapter])

    useEffect(() => {
        if (!open) {
            dispatch(initFormParams)
        }
    }, [open])

    return (
        <Dialog open={open}>
            <DialogContent className="max-w-4xl max-h-[80vh] min-h-[40vh] overflow-y-auto p-0">
                <DialogHeader className="mt-0 border-b h-18 sticky top-0 p-4 z-10 flex flex-col bg-dinoBotWhite">
                    <DialogTitle className="flex items-center justify-between">
                        <h2 className="text-lg font-bold">{title}</h2>
                        <Button
                            className="bg-dinoBotWhite m-0 text-lg hover:bg-dinoBotWhite text-dinoBotGray"
                            variant="ghost"
                            onClick={() => {
                                setOpen(false)
                            }}
                        >
                            <XIcon />
                        </Button>
                    </DialogTitle>
                </DialogHeader>

                <div className=" px-4">
                    <FormParamsPromptPreview
                        type={type}
                        setPreview={setPreview}
                        dispatch={dispatch}
                        formState={formState}
                        startTransition={startTransition}
                    />
                    {isLoading ? (
                        <div className="flex items-center justify-center w-full h-52">
                            <Loader2 className="animate-spin" />
                        </div>
                    ) : (
                        preview &&
                        preview.map((question, index) => (
                            <article
                                key={index}
                                className="border rounded-lg p-6 mb-6 bg-gray-50"
                            >
                                <header>
                                    <h2 className="text-lg font-bold text-[#FF7A00] mb-4">
                                        {`Question ${index + 1}:${question.knowledge}`}
                                    </h2>
                                </header>
                                <section>
                                    <MemoizedReactMarkdown
                                        remarkPlugins={[remarkGfm, remarkMath]}
                                        rehypePlugins={[
                                            rehypeRaw,
                                            rehypemathjax
                                        ]}
                                        components={{
                                            p({ children }) {
                                                return (
                                                    <p className="mb-2 last:mb-0">
                                                        {children}
                                                    </p>
                                                )
                                            }
                                        }}
                                    >
                                        {question.question || ''}
                                    </MemoizedReactMarkdown>

                                    <section className="border-l-4 border-l-green-500 px-2 bg-dinoBotWhite">
                                        <h3 className="text-md font-bold mb-2">
                                            Solution
                                        </h3>
                                        <MemoizedReactMarkdown
                                            remarkPlugins={[
                                                remarkGfm,
                                                remarkMath
                                            ]}
                                            rehypePlugins={[
                                                rehypeRaw,
                                                rehypemathjax
                                            ]}
                                            components={{
                                                p({ children }) {
                                                    return (
                                                        <p className="mb-2 last:mb-0">
                                                            {children}
                                                        </p>
                                                    )
                                                }
                                            }}
                                        >
                                            {question.solution || ''}
                                        </MemoizedReactMarkdown>
                                    </section>
                                </section>
                            </article>
                        ))
                    )}

                    <Accordion type="single" collapsible className="w-full">
                        <AccordionItem value="prompt">
                            <AccordionTrigger className="px-2 bg-gray-300 rounded-t-sm border border-gray-400">
                                Afficher le prompt complet
                            </AccordionTrigger>
                            <AccordionContent className="p-3 border border-gray-400 border-t-transparent">
                                <pre className="bg-gray-200 border-gray-400 border p-4 overflow-x-auto text-sm font-mono whitespace-pre-wrap">
                                    {prompt?.prompt}
                                </pre>
                            </AccordionContent>
                        </AccordionItem>
                    </Accordion>
                </div>
                <DialogFooter className="border-t border-t-gray-200 sticky bottom-0 bg-dinoBotWhite z-10 ">
                    <div className="flex justify-end p-4  w-full">
                        <Button
                            variant="outline"
                            className="bg-dinoBotLightGray text-dinoBotDarkGray border border-gray-300 shadow-none"
                            onClick={() => setOpen(false)}
                        >
                            Fermer
                        </Button>
                    </div>
                </DialogFooter>
            </DialogContent>

            <DialogTrigger asChild onClick={() => setOpen(true)}>
                <span className="bg-dinoBotVividOrange hover:bg-dinoBotVividOrange w-fit p-1 px-2 flex items-center rounded-sm text-white font-light h-fit cursor-pointer text-sm">
                    <Eye className="mr-2" size={16} /> Previsualiser{' '}
                </span>
            </DialogTrigger>
        </Dialog>
    )
}

export default EvalDialog
