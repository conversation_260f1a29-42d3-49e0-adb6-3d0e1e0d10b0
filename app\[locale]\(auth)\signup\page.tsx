import { auth } from '@/auth'
import SignupForm from '@/components/signup/signup-form'
import { Session } from '@/lib/types'
import { redirect } from '@/i18n/routing'
import { getLocale } from 'next-intl/server'
import React from 'react'
import { RegistrationType } from '@/types/types'

export default async function SignupPage() {
    const session = (await auth()) as Session
    const locale = await getLocale()
    const appEnv = process.env.APP_ENV || 'DEV'
    const registrationType =
        process.env.REGISTRATION_TYPE != ''
            ? (process.env.REGISTRATION_TYPE as RegistrationType)
            : 'none'

    if (session) {
        redirect({ href: '/', locale })
    }

    if (process.env.REGISTRATION_TYPE === 'none') {
        redirect({ href: '/login', locale })
    }

    return (
        <main className="flex flex-col size-full overflow-hidden">
            <SignupForm registrationType={registrationType} />
        </main>
    )
}
