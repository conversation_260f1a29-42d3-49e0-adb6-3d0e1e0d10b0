import * as React from 'react'

import { ButtonScrollToBottom } from '@/components/buttons/button-scroll-to-bottom'
import { FooterText } from '@/components/footer'
//import { ChatShareDialog } from '@/components/chat-share-dialog'
import { useCookies } from 'next-client-cookies'
import { Session } from '@/lib/types'
import { TrainingPromptForm } from './training-prompt-form'
import { ChatRequestOptions } from 'ai'
import { messageFileType } from '../../../../(chat)/components/chat'

export interface TrainingChatPanelProps {
    input: string
    setInput: (
        e:
            | React.ChangeEvent<HTMLInputElement>
            | React.ChangeEvent<HTMLTextAreaElement>
    ) => void
    imageFile: string
    setImageFile: (value: string) => void
    fileExtension: string
    setFileExtension: (value: string) => void
    isAtBottom: boolean
    status: 'submitted' | 'streaming' | 'ready' | 'error'
    scrollToBottom: () => void
    session?: Session
    addFileData: (data: messageFileType) => void
    handleSubmit: (
        event?: {
            preventDefault?: () => void
        },
        chatRequestOptions?: ChatRequestOptions
    ) => void
}

export function TrainingChatPanel({
    input,
    setInput,
    imageFile,
    setImageFile,
    fileExtension,
    setFileExtension,
    isAtBottom,
    scrollToBottom,
    addFileData,
    handleSubmit,
    status,
    session
}: TrainingChatPanelProps) {
    const cookies = useCookies()

    const restrictFreeUser = cookies.get('restrictFreeUser')

    return (
        <div className="w-full bg-gradient-to-b from-muted/30 from-0% to-muted/30 to-50% duration-300 ease-in-out animate-in dark:from-background/10 dark:from-10% dark:to-background/80">
            <ButtonScrollToBottom
                isAtBottom={isAtBottom}
                scrollToBottom={scrollToBottom}
            />

            <div className="mx-auto sm:max-w-2xl sm:px-4">
                <div className="mx-4 border-t rounded-md bg-background px-2 shadow-lg sm:rounded-t-xl sm:border md:pb-2 sm:mx-0">
                    <TrainingPromptForm
                        disabled={
                            !session?.user && restrictFreeUser === 'yes'
                                ? true
                                : false
                        }
                        input={input}
                        setInput={setInput}
                        imageFile={imageFile}
                        setImageFile={setImageFile}
                        fileExtension={fileExtension}
                        setFileExtension={setFileExtension}
                        status={status}
                        addFileData={addFileData}
                        HandleSubmit={handleSubmit}
                    />
                    <FooterText className="hidden sm:block" />
                </div>
            </div>
        </div>
    )
}
