'use client'

import React, { useEffect } from 'react'
import CoursesList from './components/courses-list'
import { useLocale, useTranslations } from 'next-intl'
import { useQuery } from '@tanstack/react-query'
import { getFeatureRoutesByFeatureName } from '@/lib/features/actions'
import { FeatureFlagName } from '@prisma/client'
import { notFound, useRouter } from 'next/navigation'
import { spinner } from '@/components/stocks/spinner'

function MyCourses() {
    const translate = useTranslations('app.courses.index')
    const router = useRouter()
    const locale = useLocale()

    const { data: featureFlags, isLoading } = useQuery({
        queryKey: ['featureFlags', FeatureFlagName.STUDENT_CLASSES_VIEW],
        queryFn: () =>
            getFeatureRoutesByFeatureName(FeatureFlagName.STUDENT_CLASSES_VIEW)
    })

    useEffect(() => {
        // We run the effect when loading is done and we have feature flags data.
        if (
            !isLoading &&
            featureFlags &&
            Array.isArray(featureFlags) &&
            featureFlags[0] != null
        ) {
            if (featureFlags[0] === 'not-found') {
                notFound()
            } else {
                router.replace(`/${locale}/${featureFlags[0]}`)
            }
        }
    }, [featureFlags, isLoading, router, locale])

    // Determine if we should show a loading state. This is true if we are fetching data
    // or if we have the data and are about to redirect.
    const showLoading =
        isLoading ||
        (featureFlags && Array.isArray(featureFlags) && featureFlags[0] != null)

    if (showLoading) {
        return (
            <div className="flex items-center justify-center h-screen w-full">
                {spinner}
            </div>
        )
    }

    // If we are not loading and not redirecting, render the page content.
    return (
        <div className="size-full bg-[#fafafa] p-6">
            <h1 className="text-dinoBotDarkGray text-3xl font-bold m-10">
                {translate('title')}
            </h1>
            <CoursesList />
        </div>
    )
}

export default MyCourses
