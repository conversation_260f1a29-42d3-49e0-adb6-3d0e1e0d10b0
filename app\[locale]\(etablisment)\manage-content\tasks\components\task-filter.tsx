'use client'
import React, { useCallback, useRef } from 'react'
import { Input } from '@/components/ui/input'
import { Search } from 'lucide-react'
import { selectTasks } from '../store/tasks.store'
import { useQueryClient } from '@tanstack/react-query'
import { selectUseContentStore } from '../../store/content.store'
import { useTranslations } from 'next-intl'

const TaskFilter = () => {
    const t = useTranslations('establishment.manage_content.tasks')
    const searchFilter = selectTasks.use.searchFilter()
    const { setSearchFilter } = selectTasks.use.actions()
    const queryClient = useQueryClient()
    const domainId = selectUseContentStore.use.domainId()
    const levelId = selectUseContentStore.use.levelId()
    const timeoutRef = useRef<NodeJS.Timeout | null>(null)

    // Debounce manuel pour éviter trop de requêtes
    const debouncedInvalidate = useCallback(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
        }
        timeoutRef.current = setTimeout(() => {
            queryClient.invalidateQueries({
                queryKey: ['chapters-with-tasks', domainId, levelId]
            })
        }, 500)
    }, [queryClient, domainId, levelId])

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value
        setSearchFilter(value)

        // Invalider les requêtes pour refetch avec le nouveau filtre
        debouncedInvalidate()
    }

    return (
        <div className="relative w-80">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
                type="text"
                placeholder={t('search_placeholder')}
                value={searchFilter}
                onChange={handleSearchChange}
                className="pl-10 pr-4 py-2"
            />
        </div>
    )
}

export default TaskFilter
