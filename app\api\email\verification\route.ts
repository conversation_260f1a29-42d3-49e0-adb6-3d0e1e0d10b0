import { NextRequest, NextResponse } from 'next/server'
import { sendCompleteInscriptionEmail } from '@/lib/mail-utils'
import { User } from '@/prisma/generated/zod/modelSchema/UserSchema'

type EmailRequest = {
    emailSequence: 1 | 2 | 3
    users: User[]
}[]

export async function POST(request: NextRequest) {
    try {
        const authHeader = request.headers.get('authorization')
        const token = authHeader?.replace('Bearer ', '')

        if (token !== process.env.PORTAL_API_KEY) {
            return new Response('Unauthorized', { status: 401 })
        }
        const emailRequests: EmailRequest = await request.json()

        if (!emailRequests || emailRequests.length === 0) {
            return NextResponse.json(
                {
                    success: false,
                    message: 'No email requests provided'
                },
                { status: 400 }
            )
        }

        // Traiter chaque requête email dans le tableau
        const allResults = await Promise.allSettled(
            emailRequests.map(async emailRequest => {
                const { users, emailSequence } = emailRequest

                if (!users || users.length === 0) {
                    return { emailSequence, users: [], results: [] }
                }

                const emailResults = await Promise.allSettled(
                    users.map(async user => {
                        await sendCompleteInscriptionEmail(
                            user.email,
                            '',
                            user.type,
                            emailSequence
                        )
                        return {
                            userId: user.id,
                            email: user.email,
                            success: true
                        }
                    })
                )

                return {
                    emailSequence,
                    users,
                    results: emailResults
                }
            })
        )

        // Calculer les statistiques globales
        let totalUsers = 0
        let totalSuccessful = 0
        let totalFailed = 0
        const allFailedEmails: any[] = []

        allResults.forEach((result, requestIndex) => {
            if (result.status === 'fulfilled') {
                const { results, users } = result.value
                totalUsers += users.length

                results.forEach((emailResult, userIndex) => {
                    if (emailResult.status === 'fulfilled') {
                        totalSuccessful++
                    } else {
                        totalFailed++
                        allFailedEmails.push({
                            requestIndex,
                            user: users[userIndex],
                            error: emailResult.reason
                        })
                    }
                })
            }
        })

        return NextResponse.json({
            success: true,
            message: `Email requests processed`,
            stats: {
                totalRequests: emailRequests.length,
                totalUsers,
                successful: totalSuccessful,
                failed: totalFailed
            },
            failedEmails: allFailedEmails
        })
    } catch (error) {
        console.error('Error processing email requests:', error)
        return NextResponse.json(
            { success: false, error: 'Failed to process email requests' },
            { status: 500 }
        )
    }
}
