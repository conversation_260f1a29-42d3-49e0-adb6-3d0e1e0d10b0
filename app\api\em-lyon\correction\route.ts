import { updateExamPaper } from '@/lib/control-mode/services/class/exam-papers/server'
import { ExamPapersPartialSchema } from '@/prisma/generated/zod/modelSchema/ExamPapersSchema'
import { NextRequest, NextResponse } from 'next/server'
import { logger } from '@/logger/logger'
//import { clients, notifyClients } from "@/lib/sse/notifier";

export async function POST(req: NextRequest) {
    try {
        logger.debug(`POST request received: ${req.url}`)
        const body = await req.formData()

        // Extraction des champs depuis formData
        const id = body.get('id') as string
        const mark = body.get('mark')
            ? parseFloat(body.get('mark') as string)
            : undefined
        const status = body.get('status')
            ? JSON.parse(body.get('status') as string)
            : undefined
        const correctedPaperFile = await (
            body.get('correctedPaperFile') as File
        ).arrayBuffer()

        const correctedPaperFileBuffer = Buffer.from(correctedPaperFile)

        // Validation des données avec Zod
        const validatedData = ExamPapersPartialSchema.parse({
            id,
            mark,
            status,
            correctedPaperFile: correctedPaperFileBuffer
        })

        // logger.debug(`validatedData: ${JSON.stringify(validatedData)}`)

        // Appel de la fonction métier
        await updateExamPaper(validatedData, status == false ? true : false)

        // notifyClients({
        //   id: `${id}`,
        //   status: status,
        //   mark: mark,
        // })

        // logger.debug(`notifyClients: ${JSON.stringify({
        //   id: `${id}`,
        //   status: status,
        //   mark: mark,
        // })}`)

        // Retourner une réponse de succès
        return new NextResponse(JSON.stringify({ success: true }), {
            status: 200
        })
    } catch (error) {
        console.error('Error handling POST request:', error)

        return new NextResponse(
            JSON.stringify({ error: 'Failed to process request' }),
            {
                status: 500,
                headers: { 'Content-Type': 'application/json' }
            }
        )
    }
}

// export async function GET(req: NextRequest) {
//   try {

//     logger.debug(`GET request received: ${req.url}`)
//     const { searchParams } = new URL(req.url);

//     const id = searchParams.get('id') || "";

//     logger.debug(`id: ${id}`)

//     const stream = new ReadableStream({
//       start(controller) {
//         clients.add({
//           id,
//           client: controller
//         });
//       },
//       cancel() {
//         // Supprime le client de la liste
//         clients.forEach(client => {
//           if (client.id === id) clients.delete(client);
//         });
//       }
//     });

//     return new Response(stream, {
//       headers: {
//         "Content-Type": "text/event-stream",
//         "Cache-Control": "no-cache",
//         Connection: "keep-alive",
//       },
//     });
//   } catch (error) {
//     logger.error(`error: ${error}`)
//     return new Response(JSON.stringify({ error: "An unexpected error occurred." }), {
//       status: 500,
//       headers: {
//         "Content-Type": "application/json",
//       },
//     });
//   }
// }
