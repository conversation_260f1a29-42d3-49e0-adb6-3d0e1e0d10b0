import { NextRequest, NextResponse } from 'next/server'
import { sendRequestExtensionResponseEmail } from '@/lib/mail-utils'
import { z } from 'zod'
import RequestStatusSchema from '@/prisma/generated/zod/inputTypeSchemas/RequestStatusSchema'

type RequestExtensionResponseRequest = {
    firstName: string
    lastName: string
    email: string
    status: z.infer<typeof RequestStatusSchema>
    levelsDomain: string[]
    requestDate: string
    adminComment?: string
}

export async function POST(request: NextRequest) {
    try {
        const authHeader = request.headers.get('authorization')
        const token = authHeader?.replace('Bearer ', '')

        if (token !== process.env.PORTAL_API_KEY) {
            return new Response('Unauthorized', { status: 401 })
        }

        const requestData: RequestExtensionResponseRequest =
            await request.json()

        if (!requestData) {
            return NextResponse.json(
                {
                    success: false,
                    message: 'No request data provided'
                },
                { status: 400 }
            )
        }

        const validationErrors: string[] = []

        if (!requestData.email) validationErrors.push('Email is required')
        if (!requestData.firstName)
            validationErrors.push('First name is required')
        if (!requestData.lastName)
            validationErrors.push('Last name is required')
        if (
            !requestData.status ||
            !RequestStatusSchema.options.includes(requestData.status)
        ) {
            validationErrors.push('Status must be "approved" or "rejected"')
        }
        if (
            !requestData.levelsDomain ||
            requestData.levelsDomain.length === 0
        ) {
            validationErrors.push('At least one levels domain is required')
        }
        if (!requestData.requestDate)
            validationErrors.push('Request date is required')
        if (validationErrors.length > 0) {
            return NextResponse.json(
                {
                    success: false,
                    message: 'Invalid request data',
                    errors: validationErrors
                },
                { status: 400 }
            )
        }

        try {
            await sendRequestExtensionResponseEmail(
                requestData.email,
                requestData.firstName,
                requestData.lastName,
                requestData.status,
                requestData.levelsDomain,
                requestData.requestDate,
                requestData.adminComment
            )

            return NextResponse.json({
                success: true,
                message: `Request extension response email sent successfully`,
                data: {
                    email: requestData.email,
                    firstName: requestData.firstName,
                    lastName: requestData.lastName,
                    status: requestData.status,
                    levelsDomain: requestData.levelsDomain,
                    requestDate: requestData.requestDate,
                    adminComment: requestData.adminComment
                }
            })
        } catch (emailError) {
            console.error(
                `Failed to send email to ${requestData.email}:`,
                emailError
            )
            return NextResponse.json(
                {
                    success: false,
                    message: 'Failed to send email',
                    error:
                        emailError instanceof Error
                            ? emailError.message
                            : 'Unknown error',
                    data: {
                        email: requestData.email,
                        firstName: requestData.firstName,
                        lastName: requestData.lastName,
                        status: requestData.status
                    }
                },
                { status: 500 }
            )
        }
    } catch (error) {
        console.error(
            'Error processing request extension response email:',
            error
        )
        return NextResponse.json(
            {
                success: false,
                error: 'Failed to process request extension response email',
                message:
                    error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        )
    }
}
