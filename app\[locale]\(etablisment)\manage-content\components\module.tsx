'use client'
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger
} from '@/components/ui/accordion'
import { getDomainByLevelId } from '@/lib/domains/actions'
import { getLevelsBySchoolCycle } from '@/lib/levels/actions'
import React, { useCallback, useEffect } from 'react'
import DomainLevelSelect from './domain-level-select'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useContentStore } from '../store/content.store'
import { getSchoolCycles } from '@/lib/school-cycle/actions'
import { toast } from 'sonner'
import { useTranslations, useLocale } from 'next-intl'
import { getLangProps } from '@/lib/utils/string.utils'

const ModulePageContent = () => {
    const t = useTranslations('establishment.manage_content.errors')
    const locale = useLocale()
    const {
        setDomains,
        setLevels,
        setSchoolCycles,
        schoolCycles,
        levelId,
        schoolCycleId,
        setSchoolCycleId
    } = useContentStore()

    const fetchSchoolCycles = useCallback(async () => {
        try {
            const getschoolCycles = await getSchoolCycles()
            if ('error' in getschoolCycles) {
                throw new Error()
            }
            const schoolCycles = getschoolCycles.filter(
                cycle => cycle.name !== 'Autre'
            )
            setSchoolCycles(schoolCycles)
        } catch (error) {
            toast.error(t('school_cycles'))
        }
    }, [setSchoolCycles, t])

    const fetchLevels = useCallback(
        async (schoolcycleId?: number) => {
            try {
                const levels = await getLevelsBySchoolCycle(schoolcycleId)
                if ('error' in levels) {
                    throw new Error()
                }
                setLevels(levels)
            } catch (error) {
                toast.error(t('levels'))
            }
        },
        [setLevels, t]
    )

    const fetchDomains = useCallback(async () => {
        try {
            const domains = await getDomainByLevelId(levelId)
            if ('error' in domains) {
                throw new Error()
            }
            setDomains(domains)
        } catch (error) {
            toast.error(t('domains'))
        }
    }, [setDomains, levelId, t])

    useEffect(() => {
        fetchSchoolCycles()
    }, [fetchSchoolCycles])

    useEffect(() => {
        if (schoolCycleId) {
            fetchLevels(schoolCycleId)
        }
    }, [schoolCycleId, fetchLevels])
    useEffect(() => {
        if (levelId) fetchDomains()
    }, [fetchDomains, levelId])
    return (
        <div className="bg-dinoBotDarkRed  h-[91.9%] 2xl:h-[95%] px-3 text-dinoBotWhite rounded-bl-lg">
            <ScrollArea className="w-full h-full">
                <Accordion type="single" collapsible>
                    {schoolCycles.map((cycle, index) => (
                        <AccordionItem
                            key={index}
                            value={cycle.id.toString()}
                            className="border-b-0"
                        >
                            <AccordionTrigger
                                onClick={() => setSchoolCycleId(cycle.id)}
                                className="flex-row-reverse justify-end gap-2 pl-2 data-[state=open]:font-extrabold"
                                color="white"
                            >
                                {getLangProps({
                                    obj: cycle,
                                    base: 'name',
                                    lang: locale
                                })}
                            </AccordionTrigger>
                            <AccordionContent className="pl-2">
                                <DomainLevelSelect />
                            </AccordionContent>
                        </AccordionItem>
                    ))}
                </Accordion>
            </ScrollArea>
        </div>
    )
}

export default ModulePageContent
