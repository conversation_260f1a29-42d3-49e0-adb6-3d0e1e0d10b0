import withCheckR<PERSON> from '@/components/hoc/with-check-role'
import { UserType } from '@/lib/types'
import UserTypeSchema from '@/prisma/generated/zod/inputTypeSchemas/UserTypeSchema'
import React from 'react'

interface AppLayoutProps {
    children: React.ReactNode
}

function Layout({ children }: AppLayoutProps) {
    return <>{children}</>
}

export default withCheckRole([
    UserType.STUDENT,
    UserTypeSchema.enum.establishment
])(Layout)
