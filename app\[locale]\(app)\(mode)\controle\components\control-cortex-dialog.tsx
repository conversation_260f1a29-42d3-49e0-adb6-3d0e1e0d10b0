'use client'
import React from 'react'
import { useEffect, useRef, useState } from 'react'
import useCortexStore from '@/app/[locale]/(app)/(mode)/controle/store/control-cortex-store'
import { X } from 'lucide-react'
import 'react-quill-new/dist/quill.snow.css'
import { Button } from '@/components/ui/button'
const { MathfieldElement } = await import('mathlive')

MathfieldElement.fontsDirectory = '/mathlive-fonts'
MathfieldElement.soundsDirectory = null

function ControlCortexDialog() {
    const [visible, setVisible] = useState(false)
    const mf = useRef<InstanceType<typeof MathfieldElement>>(null)
    const mfp = useRef<HTMLDivElement>(null)

    const {
        closeCortexDialog,
        open,
        insertNow,
        setCortexValue,
        cortexValue,
        idanswer,
        idcontent
    } = useCortexStore()

    useEffect(() => {
        const El = new MathfieldElement()
        mf.current?.appendChild(El as Node)
        mf.current?.focus()

        if (typeof window !== 'undefined' && window.mathVirtualKeyboard) {
            window.mathVirtualKeyboard.container = mfp.current
        }

        if (open) {
            setVisible(true)
            window.mathVirtualKeyboard?.show()
        } else {
            setVisible(false)
            window.mathVirtualKeyboard?.hide()
        }
        mfp.current
            ?.querySelectorAll('[data-tooltip="Lettres romaines"]')
            .forEach(e => e.remove())
    }, [open])

    return (
        <div
            className={`${open ? 'flex' : 'hidden'} bg-slate-100 border-2 border-dinoBotDarkGray rounded-sm flex-col justify-between gap-2 absolute top-1/2 -translate-x-1/2 left-1/2 -translate-y-1/2 size-fit p-2 ${visible ? 'h-80' : 'h-fit'}`}
        >
            <div className="flex h-fit">
                <div className="w-96">
                    {/* @ts-expect-error Explanation: main-field dont have a import */}
                    <math-field
                        ref={mf}
                        onInput={(evt: React.ChangeEvent<any>) =>
                            setCortexValue(
                                evt.target.value,
                                idanswer,
                                idcontent
                            )
                        }
                        style={{ width: '100%' }}
                    >
                        {cortexValue[idanswer]?.[idcontent] || ''}
                        {/* @ts-expect-error Explanation: main-field dont have a import */}
                    </math-field>
                </div>
                <div className="size-full sm:w-fit flex flex-row-reverse sm:flex-row justify-center items-center gap-2">
                    <Button
                        className="bg-dinoBotRed text-white hover:bg-dinoBotRed/80 transition-all duration-200 rounded h-10"
                        onClick={insertNow}
                    >
                        Insérer
                    </Button>
                    <Button
                        className="h-10"
                        variant={'outline'}
                        onClick={closeCortexDialog}
                    >
                        <X />
                    </Button>
                </div>
            </div>
            <div
                ref={mfp}
                className={`${visible ? 'h-72' : 'h0'} w-[530px]`}
            ></div>
        </div>
    )
}

export default ControlCortexDialog
