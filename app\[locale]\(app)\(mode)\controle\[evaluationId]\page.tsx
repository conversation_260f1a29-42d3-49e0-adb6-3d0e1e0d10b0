import React from 'react'
import Timer from './components/Timer/timer'
import ProgrammedEvaluationPreview from './components/programmed-evaluation-preview'
import {
    getPlannedEvaluationAndEvaluationById,
    getSignedControlMedia
} from '@/lib/control-mode/actions'
import { PlannedEvaluationWithPartialRelations } from '@/prisma/generated/zod/modelSchema/PlannedEvaluationSchema'
import ProgrammedAnswerControl from './components/programmed-answer-evaluation'
import {
    ControlPartialWithRelations,
    ControlWithPartialRelations,
    ControlWithPartialRelationsSchema
} from '@/prisma/generated/zod/modelSchema/ControlSchema'
import MediaFullView from './components/assets-viewer/media-full-view'
import { ControlFeedbackOutput } from '@/lib/control-mode/types/control/types'
import { z } from 'zod'

const CONTROL_TIME: number = Number.parseInt(process.env.CONTROL_TIME || '3600')

async function Evaluation(props: {
    params: Promise<{ evaluationId: string }>
}) {
    const params = await props.params
    const time = new Date()

    let plannedEvaluation = (await getPlannedEvaluationAndEvaluationById(
        params.evaluationId
    )) as PlannedEvaluationWithPartialRelations

    let mediaExpiresIn = plannedEvaluation.timeLimit ?? 3600

    async function transformControleData(
        data: z.infer<typeof ControlWithPartialRelationsSchema>
    ): Promise<ControlWithPartialRelations | null> {
        if (!data) {
            return null
        }

        const exercises = await Promise.all(
            data?.exercises?.map(async (item, i) => {
                const questions = await Promise.all(
                    item?.questions?.map(async (q, i) => {
                        const medias = q?.medias
                            ? await Promise.all(
                                  q.medias.map(async m => {
                                      const url = m.fileUrl
                                          ? await getSignedControlMedia(
                                                m.fileUrl,
                                                mediaExpiresIn
                                            )
                                          : undefined
                                      return {
                                          ...m,
                                          signedUrl: url
                                      }
                                  })
                              )
                            : undefined

                        return {
                            id: q.id,
                            content: q.content,
                            answer: undefined,
                            feedback: q.solution,
                            desmosCode: q?.desmosCode,
                            type: q?.type,
                            medias
                        }
                    }) || []
                )
                const medias =
                    item && item.medias
                        ? await Promise.all(
                              item.medias.map(async m => {
                                  const url = m.fileUrl
                                      ? await getSignedControlMedia(
                                            m.fileUrl,
                                            mediaExpiresIn
                                        )
                                      : undefined
                                  return {
                                      ...m,
                                      signedUrl: url
                                  }
                              })
                          )
                        : []

                return {
                    ...item,
                    id: item.id,
                    title: 'exercice: ' + (i + 1),
                    score: 0,
                    questions,
                    medias
                }
            }) || []
        )

        const transformedData: ControlWithPartialRelations = {
            ...data,
            exercises: exercises as any
        }

        return transformedData
    }
    const controle = await transformControleData(
        plannedEvaluation.control! as ControlWithPartialRelations
    )

    plannedEvaluation = {
        ...plannedEvaluation,
        control: controle as any
    } as PlannedEvaluationWithPartialRelations

    time.setSeconds(time.getSeconds() + (plannedEvaluation?.timeLimit ?? 0))

    return (
        <div className="flex size-full flex-col xl:flex-row relative">
            <div className="xl:absolute fixed xl:top-5 xl:bottom-auto bottom-2 left-1/2 -translate-x-1/2  z-20">
                <Timer timer={time} timeLimit={plannedEvaluation?.timeLimit} />
            </div>
            <div className=" xl:w-[50vw] w-full border-l-gray-700 border-2 relative">
                <ProgrammedEvaluationPreview
                    control={
                        plannedEvaluation.control! as ControlWithPartialRelations
                    }
                    availableDate={plannedEvaluation?.availableDate}
                    plannedEvaluation={plannedEvaluation}
                />
            </div>
            <div className=" xl:w-[50vw] w-full border-r-gray-700 border-2">
                <ProgrammedAnswerControl
                    plannedEvaluation={plannedEvaluation}
                />
            </div>
            <MediaFullView />
        </div>
    )
}

export default Evaluation
