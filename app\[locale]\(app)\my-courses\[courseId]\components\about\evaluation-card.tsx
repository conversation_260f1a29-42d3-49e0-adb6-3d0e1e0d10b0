import { ControlPartialWithRelations } from '@/prisma/generated/zod/modelSchema/ControlSchema'
import { Eye, Home, Timer } from 'lucide-react'
import React from 'react'
import { useRouter } from '@/i18n/routing'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'
import { useCookies } from 'next-client-cookies'

type EvaluationCardProps = {
    evaluation?: ControlPartialWithRelations
    isEvalOrTask?: boolean
    plannedId?: string
    availableDate?: Date
}

const EvaluationCard = ({
    evaluation,
    isEvalOrTask = true,
    plannedId,
    availableDate
}: EvaluationCardProps) => {
    const route = useRouter()
    const translate = useTranslations('app.courses.index')
    const cookies = useCookies()

    return (
        <div
            className={`border-2 rounded-lg ${isEvalOrTask ? 'border-dinoBotRedOrange/60' : 'border-dinoBotVibrantBlue/60'}  w-72 min-h-28 p-2`}
        >
            <div className="flex justify-between items-center">
                <div className="flex gap-2 items-center">
                    {/* <Checkbox id="terms" className={`size-5 ${isEvalOrTask?'data-[state=checked]:bg-dinoBotVividOrange border-dinoBotRedOrange':'data-[state=checked]:bg-dinoBotVibrantBlue border-dinoBotVibrantBlue'} `}/> */}
                    <h3
                        className={`${isEvalOrTask ? 'text-dinoBotRedOrange' : 'text-dinoBotVibrantBlue'} flex gap-2`}
                    >
                        {isEvalOrTask ? (
                            <>
                                <Timer />
                                <span className="mt-auto">
                                    {translate('card.evaluation')}
                                </span>
                            </>
                        ) : (
                            <>
                                <Home />
                                <span className="mt-auto">
                                    {translate('card.form')}
                                </span>
                            </>
                        )}
                    </h3>
                </div>
                <Eye
                    className="cursor-pointer"
                    onClick={() => {
                        const canNotPassTest =
                            availableDate && new Date() < availableDate
                        if (canNotPassTest) {
                            toast.error('Pas disponible encore')
                            return
                        }
                        cookies.set('feature', 'Ctrl')
                        route.replace(`/controle/${plannedId}`)
                    }}
                />
            </div>
            <p className="text-dinoBotGray">
                {evaluation?.status == 'ASSIGNED'
                    ? ''
                    : translate('card.draft')}
            </p>
            <p className="text-wrap line-clamp-3">{evaluation?.name}</p>
        </div>
    )
}

export default EvaluationCard
