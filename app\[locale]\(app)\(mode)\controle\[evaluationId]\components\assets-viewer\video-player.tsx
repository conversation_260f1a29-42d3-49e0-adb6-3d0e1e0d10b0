'use client'

import ImageNotFound from './image-not-found'

interface Props {
    url: string
}

export default function VideoPlayer({ url }: Props) {
    return (
        <div className="w-full h-[150px] flex items-center bg-dinoBotLightGray/30 rounded">
            {url ? (
                <video controls width="640" height="360" src={url} />
            ) : (
                <ImageNotFound />
            )}
        </div>
    )
}
