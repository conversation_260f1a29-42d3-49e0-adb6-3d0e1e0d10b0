import React from 'react'
import { auth } from '@/auth'
import { Session } from '@/lib/types'
import { getMissingKeys } from '@/app/[locale]/actions'
import { redirect } from '@/i18n/routing'
import { getLocale } from 'next-intl/server'
import { notFound } from 'next/navigation'
import Chat from './components/chat'
import {
    getFeatureRoutesByFeatureName,
    getFeatureFlags
} from '@/lib/features/actions'
import { FeatureFlagName } from '@prisma/client'

export const metadata = {
    title: 'DinoBot - Votre assistant scolaire personnel'
}

export default async function IndexPage() {
    const session = (await auth()) as Session
    const locale = await getLocale()
    const missingKeys = await getMissingKeys()

    const featureFlags = await getFeatureRoutesByFeatureName(
        FeatureFlagName.STUDENT_CHAT_MODE
    )

    if (Array.isArray(featureFlags) && featureFlags[0] != null) {
        if (featureFlags[0] === 'not-found') notFound()
        redirect({ href: `/${featureFlags[0]}`, locale })
    }

    if (session?.user?.type === 'teacher')
        redirect({ href: '/created-controls', locale })

    return (
        // <AI initialAIState={{ chatId: id, topic: '', messages: [], saved: false, chatPath: '' }}>
        //   <Chat
        //     //id={id}
        //     session={session}
        //     missingKeys={missingKeys} />
        // </AI>
        <Chat session={session} missingKeys={missingKeys} />
    )
}
