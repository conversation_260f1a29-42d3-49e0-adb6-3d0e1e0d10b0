import { selectUseAvatarStore } from '@/lib/stores/avatar-store/avatar-store'
import useStreamingStore from '@/lib/stores/text-streaming-store/store'
import { Session } from '@/lib/types'
import { Message, UIMessage } from 'ai'
import { nanoid } from 'nanoid'
import { useCookies } from 'next-client-cookies'
import { useTranslations } from 'next-intl'
import React, { useEffect } from 'react'

type CheckMessageTramingProps = {
    messages: UIMessage[]
    session?: Session
    setMessages: (
        messages: Message[] | ((messages: Message[]) => Message[])
    ) => void
}

const CheckMessageTraning = ({
    messages,
    setMessages,
    session
}: CheckMessageTramingProps) => {
    const t = useTranslations('app.mode.train')
    const cookies = useCookies()
    const { setIsStreaming } = useStreamingStore()
    const setKnowledgeBaseModeContent =
        selectUseAvatarStore.use.setKnowledgeBaseModeContent()

    useEffect(() => {
        const messagesLength = messages?.length
        setKnowledgeBaseModeContent(
            `voicie le chat : ${JSON.stringify(messages)}`
        )
        if (messagesLength === 4 && !session?.user) {
            cookies.set('restrictFreeUser', 'yes')
            setIsStreaming(false)
            setMessages(currentMessages => [
                ...currentMessages,
                {
                    id: nanoid(),
                    role: 'assistant',
                    content: t('continue')
                }
            ])
        }
    }, [messages])

    return <></>
}

export default CheckMessageTraning
