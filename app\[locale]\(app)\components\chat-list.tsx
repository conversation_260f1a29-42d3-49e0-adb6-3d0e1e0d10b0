import { Separator } from '@/components/ui/separator'
import { SpinnerMessage } from '@/components/stocks/message'
import { ExerciseAssignmentBotMessage } from '@/components/stocks/message'
import { UIMessage } from 'ai'
import { BotErrorMessage, BotMessage, UserMessage } from './message'
import { messageFileType } from '../(chat)/components/chat'
import React, { memo } from 'react'

export interface ChatList {
    messages: UIMessage[]
    exercise: any
    status: 'submitted' | 'streaming' | 'ready' | 'error'
    messagesFileMap: Map<number, messageFileType>
    error: Error | undefined
}

export const ChatList = memo(function DisplayName({
    messages,
    exercise,
    status,
    messagesFileMap,
    error
}: ChatList) {
    if (!messages.length && !exercise) {
        return null
    }
    return (
        <div className="relative mx-auto max-w-2xl px-4">
            {exercise ? (
                <>
                    <ExerciseAssignmentBotMessage
                        content={exercise?.assignment}
                        exercise={exercise}
                    />
                    <Separator className="my-4" />
                </>
            ) : null}

            {messages.map((message, index) => {
                // Récupérer le fichier directement de la Map en utilisant l'ID du message
                const fileData = messagesFileMap.get(index)

                return (
                    <div key={`message-${message.id}-${index}`}>
                        {message.role === 'user' ? (
                            <MemoizedUserMessage
                                messageId={message.id}
                                content={message.content}
                                fileData={fileData}
                            />
                        ) : (
                            <BotMessage
                                key={`bot-${message.id}`}
                                content={message.parts
                                    .filter(part => part.type === 'text')
                                    .map(part => part.text)
                                    .join('')}
                            />
                        )}
                        {index < messages.length - 1 && (
                            <Separator className="my-4" />
                        )}
                    </div>
                )
            })}
            {error && <BotErrorMessage content={error.message} />}
            {status === 'submitted' && (
                <div className="my-4">
                    <SpinnerMessage />
                </div>
            )}
        </div>
    )
})

// Composant mémorisé pour UserMessage
const MemoizedUserMessage = memo(
    ({
        messageId,
        content,
        fileData
    }: {
        messageId: string
        content: string
        fileData: messageFileType | undefined
    }) => {
        return (
            <UserMessage
                key={`user-${messageId}`}
                audio={''}
                transcribedAudio={''}
                file={fileData}
            >
                {content}
            </UserMessage>
        )
    },
    // Fonction de comparaison personnalisée
    (prevProps, nextProps) => {
        return (
            prevProps.messageId === nextProps.messageId &&
            prevProps.content === nextProps.content &&
            prevProps.fileData?.id === nextProps.fileData?.id
        )
    }
)

// Ajouter un displayName pour faciliter le débogage
MemoizedUserMessage.displayName = 'MemoizedUserMessage'
