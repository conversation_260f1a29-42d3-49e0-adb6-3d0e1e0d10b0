'use client'

import { useRef, useState } from 'react'
import { selectUseCtrlMediaStore } from '../../../store/controle-media-store'
import {
    ChevronLeft,
    ChevronRight,
    Download,
    LoaderCircle,
    Minus,
    OctagonAlert,
    Plus,
    X
} from 'lucide-react'
import { useTranslations } from 'next-intl'
import ImageNotFound from './image-not-found'

interface ScrollStartState {
    x: number
    y: number
    scrollLeft: number
    scrollTop: number
}

function MediaFullView() {
    const attachments = selectUseCtrlMediaStore().attachments
    const attachmentIndex = selectUseCtrlMediaStore().index
    const setAttachments = selectUseCtrlMediaStore().setAttachments
    const next = selectUseCtrlMediaStore().next
    const previous = selectUseCtrlMediaStore().previous
    const [src, setSrc] = useState<string | null>(null)
    const [scale, setScale] = useState<number>(1)

    const containerRef = useRef<HTMLDivElement>(null)
    const [isDragging, setIsDragging] = useState(false)
    const [start, setStart] = useState<ScrollStartState | null>(null)

    const t = useTranslations('app.mode.controle')

    const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
        const container = containerRef.current
        if (!container) return

        setIsDragging(true)
        container.style.cursor = 'grabbing'

        setStart({
            x: e.pageX,
            y: e.pageY,
            scrollLeft: container.scrollLeft,
            scrollTop: container.scrollTop
        })
    }

    const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
        const container = containerRef.current
        if (!isDragging || !container || !start) return

        e.preventDefault() // Prevent text selection
        const dx = e.pageX - start.x
        const dy = e.pageY - start.y

        container.scrollLeft = start.scrollLeft - dx
        container.scrollTop = start.scrollTop - dy
    }

    const handleMouseUpOrLeave = () => {
        const container = containerRef.current
        setIsDragging(false)
        if (container) container.style.cursor = 'grab'
    }

    return attachments && attachments[attachmentIndex] ? (
        <div
            className={`${attachments ? 'fixed' : 'hidden'} top-10 left-1/2 -translate-x-1/2 w-4/5 h-5/6 bg-dinoBotWhite z-30 rounded-md flex flex-col border`}
        >
            <div className="w-full h-8 flex justify-between items-center">
                <div className="w-2/5 font-semibold px-1 text-dinoBotBlue truncate"></div>
                <div>
                    {attachments[attachmentIndex] &&
                    attachments[attachmentIndex].fileType?.includes('image') ? (
                        <div className="flex gap-1">
                            <button
                                className="hover:text-dinoBotBlue"
                                onClick={e => {
                                    e.stopPropagation()
                                    setScale(prev => Math.min(3, prev + 0.1))
                                }}
                            >
                                <Plus />
                            </button>
                            <div className="px-1 font-semibold">
                                {Math.ceil(scale * 100)}%
                            </div>
                            <button
                                className="hover:text-dinoBotBlue"
                                onClick={e => {
                                    e.stopPropagation()
                                    setScale(prev => Math.max(0.5, prev - 0.1))
                                }}
                            >
                                <Minus />
                            </button>
                        </div>
                    ) : null}
                </div>
                <div className="w-2/5 h-8 flex justify-end items-center hover:text-dinoBotRed">
                    <button
                        className="size-8"
                        onClick={() => {
                            setAttachments(null, 0)
                            setScale(1)
                        }}
                    >
                        <X />
                    </button>
                </div>
            </div>
            <div
                ref={containerRef}
                onMouseDown={handleMouseDown}
                onMouseMove={handleMouseMove}
                onMouseUp={handleMouseUpOrLeave}
                onMouseLeave={handleMouseUpOrLeave}
                className="max-w-full h-[calc(100%-32px)] flex justify-center items-center overflow-hidden"
            >
                {attachments[attachmentIndex] &&
                attachments[attachmentIndex].fileType?.includes('image') ? (
                    !attachments[attachmentIndex].signedUrl ? (
                        <img
                            src={attachments[attachmentIndex].signedUrl!}
                            alt={attachments[attachmentIndex].fileName!}
                            style={{ transform: `scale(${scale})` }}
                        />
                    ) : (
                        <ImageNotFound />
                    )
                ) : attachments[attachmentIndex].fileType?.includes('video') ? (
                    attachments[attachmentIndex].signedUrl ? (
                        <video
                            controls
                            src={attachments[attachmentIndex].signedUrl!}
                            className="w-full h-full bg-black/80"
                        />
                    ) : (
                        <ImageNotFound />
                    )
                ) : null}
            </div>
            {attachments.length > 1 ? (
                <>
                    <button
                        onClick={previous}
                        className="absolute top-1/2 left-2 text-3xl hover:text-dinoBotVibrantBlue duration-150 transition-colors"
                    >
                        <ChevronLeft className="size-10" />
                    </button>
                    <button
                        onClick={next}
                        className="absolute top-1/2 right-2 text-3xl hover:text-dinoBotVibrantBlue duration-150 transition-colors"
                    >
                        <ChevronRight className="size-10" />
                    </button>
                </>
            ) : null}
        </div>
    ) : null
}

export default MediaFullView
