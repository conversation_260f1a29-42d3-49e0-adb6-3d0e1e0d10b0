import { auth } from '@/auth'
import ResetPasswordFinishForm from '@/components/reset-pwd/reset-password-finish-form'
import { Session } from '@/lib/types'
import { redirect, notFound } from 'next/navigation'
import { verifyIfResetKeyIsValid } from '../../actions'
import InvalidResetKey from './invalid-key'
import React from 'react'

export interface SignUpLastStepsPageProps {
    params: Promise<{
        key: string
    }>
}

export default async function SignupLastStepsPage(
    props: SignUpLastStepsPageProps
) {
    const params = await props.params
    const session = (await auth()) as Session

    const isKeyValid = await verifyIfResetKeyIsValid(params.key)

    if (session) {
        redirect('/')
    }

    if (!params.key) {
        notFound()
    }

    return (
        <main className="flex flex-col px-4">
            {isKeyValid ? (
                <ResetPasswordFinishForm resetKey={params.key} />
            ) : (
                <InvalidResetKey />
            )}
        </main>
    )
}
