import { auth } from '@/auth'
import { Session } from '@/lib/types'
import { notFound } from 'next/navigation'
import React from 'react'
import MathJaxViewer from '@/components/katex-viewer/katex-viewer'

export default async function KatexViewerPage() {
    const session = (await auth()) as Session

    if (!session?.user || !session?.user.isAdmin) {
        notFound()
    }

    return <MathJaxViewer />
}
