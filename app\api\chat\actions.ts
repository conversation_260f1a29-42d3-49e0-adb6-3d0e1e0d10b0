import { createNewChat, updateExistingChat } from '@/app/[locale]/actions'
import { Chat, MessageWithFile, Session } from '@/lib/types'
import { logger } from '@/logger/logger'
import {
    generateText,
    StreamTextOnFinishCallback,
    ToolSet,
    UIMessage
} from 'ai'
import path from 'path'
import fs from 'fs'
import axios from 'axios'
import { getTranslations } from 'next-intl/server'
import { mistral } from '@ai-sdk/mistral'

type saveMessagesType = {
    messages: UIMessage[]
    id: string
    session: Session | null
    feature: string | undefined
    topic: string | undefined
    existingChat: Chat
    userMessageCreatedAt: Date
    currentExerciseId: string | undefined
    deleteCookies: () => void
    imageFile?: string
    fileExtension?: string
    fileBase64?: string
}

interface customUIMessage extends UIMessage {
    file: unknown
}

export const saveMessages = ({
    messages,
    id,
    session,
    currentExerciseId,
    feature,
    topic,
    userMessageCreatedAt,
    existingChat,
    deleteCookies,
    imageFile,
    fileExtension,
    fileBase64
}: saveMessagesType) => {
    const path = `/chat/${id}`
    const onFinish: StreamTextOnFinishCallback<ToolSet> = async ({ text }) => {
        try {
            if (feature === 'Exo') return
            // Récupérer le dernier message de l'utilisateur
            const lastUserMessageIndex = messages
                .map(m => m.role)
                .lastIndexOf('user')
            const lastUserMessage =
                lastUserMessageIndex >= 0
                    ? messages[lastUserMessageIndex]
                    : null
            let messageWithTimestamp = lastUserMessage as customUIMessage

            // Si c'est le premier message (message utilisateur) et qu'un fichier existe
            if (lastUserMessage?.role === 'user' && imageFile) {
                messageWithTimestamp = {
                    ...messageWithTimestamp,
                    createdAt:
                        lastUserMessage?.createdAt ||
                        userMessageCreatedAt ||
                        new Date(),
                    file: {
                        name: `${imageFile}-${Date.now()}.${fileExtension || 'png'}`,
                        file: fileBase64,
                        createdAt: new Date()
                    }
                }
            }
            // Créer la réponse finale de l'assistant
            const finalResponse = {
                role: 'assistant',
                content: text,
                id: Date.now().toString()
            }

            // S'assurer que l'ordre est toujours [user, assistant]
            const messagesToStore = lastUserMessage
                ? [messageWithTimestamp, finalResponse]
                : [finalResponse]

            // Log pour déboguer
            logger.debug(
                `Messages to store: ${messagesToStore.map(m => m.role).join(', ')}`
            )

            let title = ''
            if (lastUserMessage?.content) {
                if (
                    typeof lastUserMessage.content === 'string' &&
                    lastUserMessage.content.startsWith('msgllm4')
                ) {
                    const endDocIndex = lastUserMessage.content.indexOf(
                        'Ceci est la fin du document.'
                    )
                    if (endDocIndex !== -1) {
                        title = lastUserMessage.content
                            .substring(
                                endDocIndex +
                                    'Ceci est la fin du document.'.length
                            )
                            .substring(0, 100)
                    } else {
                        title = lastUserMessage.content.substring(0, 100)
                    }
                } else if (typeof lastUserMessage.content === 'string') {
                    title = lastUserMessage.content.substring(0, 100)
                }
            }

            const processedTitle = title
                ? title.trim()
                : `Sans titre - ${topic || `Chat`}`
            const isValidMessageRole = (msg: {
                role: string
            }): msg is MessageWithFile =>
                ['user', 'data', 'assistant', 'system'].includes(msg.role)

            // Traiter les messages et ajouter les informations de fichier
            const processedMessages = messagesToStore.filter(isValidMessageRole)

            const newChat: Chat = {
                id,
                title: processedTitle,
                userId: session?.user?.id ?? '',
                topic: topic || 'Mathématiques',
                createdAt: new Date(),
                messages: processedMessages,
                exerciseId: feature === 'Exam' ? currentExerciseId : null,
                path,
                pinned: false
            }
            logger.debug('Chat id in chat actions is :' + id)

            if (existingChat) {
                await updateExistingChat(existingChat as Chat, newChat)
            } else {
                await createNewChat(newChat).then(() => {
                    deleteCookies()
                })
            }
        } catch (error) {
            logger.error(`save Messagge Error: ${error}`)
        }
    }
    return onFinish
}

export async function sendFileToMathpixOcr(
    fileName: string,
    fileExtension: string
): Promise<{ text: string; fileBase64: string }> {
    try {
        //sendingToMathpix = true
        const uploadDir = path.join(process.cwd(), 'public', 'uploads')
        const filePath = path.join(uploadDir, fileName)
        //console.log("Uploaded file path : " + filePath);

        if (!fs.existsSync(filePath)) {
            throw new Error('File not found')
        }

        //console.log(fileExtension)

        const fileBuffer = fs.readFileSync(filePath)
        let fileBase64 = ''
        if (fileExtension === 'pdf') {
            const bodyFormData = new FormData()

            const fileBlob = new Blob([fileBuffer], { type: 'application/pdf' })

            bodyFormData.append('file', fileBlob)

            // This endpoint will launch an asynchronous process
            const response = await axios.post(
                'https://api.mathpix.com/v3/pdf',
                bodyFormData,
                {
                    headers: {
                        app_key: process.env.MATHPIX_API_KEY
                    }
                }
            )

            //console.log(response.data)

            const pdf_id = response.data.pdf_id

            // Send a GET request to check if the process is done or not, needs to be a loop until the process is done, also wait 1 second before
            let done = false
            let text = ''

            while (!done) {
                await new Promise(resolve => setTimeout(resolve, 1000))
                const response = await axios.get(
                    `https://api.mathpix.com/v3/pdf/${pdf_id}`,
                    {
                        headers: {
                            app_key: process.env.MATHPIX_API_KEY
                        }
                    }
                )
                //console.log(response.data)

                done = response.data.status === 'completed'
            }

            // Final GET to get the result lines in json format
            const finalResponse = await axios.get(
                `https://api.mathpix.com/v3/pdf/${pdf_id}.lines.json`,
                {
                    headers: {
                        app_key: process.env.MATHPIX_API_KEY
                    }
                }
            )

            //console.log(finalResponse.data)

            // response is a json object that has pages of lines so we need to concatenate the text only
            finalResponse.data.pages.forEach((page: any) => {
                page.lines.forEach((line: any) => {
                    text += line.text + '\n'
                })
            })

            //console.log(text)

            fileBase64 = `data:application/pdf;base64,${fileBuffer.toString('base64')}`

            fs.unlinkSync(filePath)

            return { text: text, fileBase64 }
        } else {
            const base64File = fileBuffer.toString('base64')
            //console.log("base64 image: " + base64File)

            // For future references, PDF files in base64 start like this : "data:application/pdf;base64,"

            fileBase64 = `data:image;base64,${base64File}`

            const response = await axios.post(
                'https://eu-central-1.api.mathpix.com/v3/text',
                {
                    src: `data:image;base64,${base64File}`
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        app_key: process.env.MATHPIX_API_KEY
                        // 'app_id': 'MATHPIX_APP_ID',
                    }
                }
            )

            fs.unlinkSync(filePath)

            return { text: response.data.text, fileBase64 }
        }
    } catch {
        //console.log("Mathpix error : " + err);
        //sendingToMathpix = false
        return { text: '', fileBase64: '' }
    }
}

export async function sendFileToMistralOcr(
    fileName: string,
    fileExtension: string
): Promise<{ text: string; fileBase64: string }> {
    try {
        const uploadDir = path.join(process.cwd(), 'public', 'uploads')
        console.log('🚀 ~ uploadDir:', uploadDir)
        const filePath = path.join(uploadDir, fileName)

        if (!fs.existsSync(filePath)) {
            throw new Error('File not found')
        }

        const fileBuffer = fs.readFileSync(filePath)
        const fileBase64 = `data:application/${fileExtension};base64,${fileBuffer.toString('base64')}`
        const fileBlob = new Blob([fileBuffer], {
            type: fileExtension === 'pdf' ? 'application/pdf' : 'image/*'
        })

        // const url = URL.createObjectURL(fileBlob)

        const MISTRAL_API_KEY = process.env.MISTRAL_API_KEY
        const payload = {
            model: 'mistral-ocr-latest',
            document: {
                type: 'document_url',
                document_url: fileBase64
            },
            include_image_base64: true
        }

        const response = await axios.post(
            'https://api.mistral.ai/v1/ocr',
            payload,
            {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${MISTRAL_API_KEY}`
                }
            }
        )

        fs.unlinkSync(filePath)
        console.log(response.data)
        if (response.data && response.data.pages) {
            const ocrResult = response.data.pages
                .map((page: any) => page.markdown)
                .join('\n\n')
            const resultPath = path.join(uploadDir, 'result.md')
            fs.writeFileSync(resultPath, JSON.stringify(ocrResult))
            return { text: ocrResult, fileBase64 }
        }
        return { text: '', fileBase64: '' }
    } catch (err) {
        console.error('Mistral OCR error:', err)
        logger.error('Error processing file with mistral ocr:', err)
        return { text: '', fileBase64: '' }
    }
}

export async function getRandomErrorMessage(): Promise<string> {
    'use server'
    try {
        const tc = await getTranslations('lib.errors')
        const randomIndex = Math.floor(
            Math.random() * Number.parseInt(tc('chat.errorTexts.length'))
        )
        return tc(`chat.errorTexts.${randomIndex}`)
    } catch (error) {
        logger.error('Error getting random error message:', error)
        return "Une erreur s'est produite. Veuillez réessayer plus tard."
    }
}
