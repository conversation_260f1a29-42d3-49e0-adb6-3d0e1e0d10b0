import { Home, Timer } from 'lucide-react'
import React from 'react'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'
import { NavigateAction, View } from 'react-big-calendar'
import {
    filterType,
    useCalendarStore
} from '@/app/[locale]/(teacher)/(work)/my-classes/store/calendar-store'
import { useLocale, useTranslations } from 'next-intl'
import { cn } from '@/lib/utils/utils'
import { getLangDir } from 'rtl-detect'
import PaginationCalendar from './pagination-calendar'

type HeaderCalendarProps = {
    label: string
    onNavigate: (action: NavigateAction, newDate?: Date) => void
    onView: (view: View) => void
}
const HeaderCalendar = ({ label, onNavigate }: HeaderCalendarProps) => {
    const t = useTranslations('teacher.myClass.calendar.tabs')
    const [, setValue] = useCalendarStore(s => [s.filter, s.setFilter])
    const locale = useLocale()
    const dir = getLangDir(locale)
    return (
        <div className="w-full flex justify-between my-2 px-1">
            <div className="flex">
                <ToggleGroup
                    type="single"
                    className={cn(dir === 'rtl' && 'flex-row-reverse')}
                    defaultValue="TOUS"
                    onValueChange={(e: filterType) => setValue(e)}
                >
                    <ToggleGroupItem
                        value="TOUS"
                        aria-label="Toggle"
                        className="data-[state=on]:bg-dinoBotGray data-[state=on]:text-dinoBotWhite"
                    >
                        {t('all')}
                    </ToggleGroupItem>
                    <ToggleGroupItem
                        value="HOMEWORK"
                        aria-label="Toggle "
                        className="text-dinoBotSky hover:text-dinoBotSky/70 data-[state=on]:text-dinoBotWhite data-[state=on]:bg-dinoBotSky "
                    >
                        <Home className="size-5" />
                        &nbsp;{t('homework')}
                    </ToggleGroupItem>
                    <ToggleGroupItem
                        value="CONTROL"
                        aria-label="Toggle"
                        className="text-dinoBotVividOrange hover:text-dinoBotVividOrange/70 data-[state=on]:text-dinoBotWhite data-[state=on]:bg-dinoBotVividOrange "
                    >
                        <Timer className="size-5" /> &nbsp; {t('control')}
                    </ToggleGroupItem>
                    {/* <ToggleGroupItem value="sortie" aria-label="Toggle" className='text-dinoBotGreen hover:text-dinoBotGreen/70 data-[state=on]:text-dinoBotWhite data-[state=on]:bg-dinoBotGreenVividOrange '>
                        Sorties
                    </ToggleGroupItem> */}
                </ToggleGroup>
            </div>
            <div className="flex">
                <PaginationCalendar label={label} onNavigate={onNavigate} />
            </div>
            <div className="flex"></div>
        </div>
    )
}

export default HeaderCalendar
